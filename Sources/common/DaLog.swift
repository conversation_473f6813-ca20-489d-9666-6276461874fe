import os

let logG = DaLog(subsystem: "com.iantsrtc.capture", category: "Log")

/// 日志级别枚举
enum LogLevel: Int, CaseIterable {
    case debug = 0
    case info = 1
    case notice = 2
    case warning = 3
    case error = 4
    case fault = 5

    var name: String {
        switch self {
        case .debug: return "DEBUG"
        case .info: return "INFO"
        case .notice: return "NOTICE"
        case .warning: return "WARNING"
        case .error: return "ERROR"
        case .fault: return "FAULT"
        }
    }
}

/// Log 类：基于 os.Logger 的二次封装
/// 提供统一的日志记录接口，支持不同的日志级别
/// 在非 DEBUG 模式下只输出 warning、error、fault 级别的日志
class DaLog {
    private let logger: Logger

    /// 当前日志级别
    private static let currentLogLevel: LogLevel = {
        #if DEBUG
        return .debug  // DEBUG 模式下输出所有级别的日志
        #else
        return .warning  // Release 模式下只输出 warning 及以上级别的日志
        #endif
    }()

    /// 初始化 Log 实例
    /// - Parameters:
    ///   - subsystem: 子系统标识符，通常使用反向域名格式，如 "com.yourcompany.yourapp"
    ///   - category: 日志分类，用于区分不同模块或功能
    init(subsystem: String, category: String) {
        self.logger = Logger(subsystem: subsystem, category: category)

        // 只在第一次初始化时打印日志级别信息
        if subsystem == "com.iantsrtc.capture" && category == "Log" {
            #if DEBUG
            logger.info("📝 DaLog 初始化 - DEBUG 模式，日志级别: \(Self.currentLogLevel.name, privacy: .public)")
            #else
            logger.warning("📝 DaLog 初始化 - RELEASE 模式，日志级别: \(Self.currentLogLevel.name, privacy: .public)")
            #endif
        }
    }

    /// 检查是否应该输出指定级别的日志
    /// - Parameter level: 日志级别
    /// - Returns: 是否应该输出
    private func shouldLog(level: LogLevel) -> Bool {
        return level.rawValue >= Self.currentLogLevel.rawValue
    }
    
    /// 调试级别日志
    /// 用于详细的调试信息，仅在调试时显示
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        guard shouldLog(level: .debug) else { return }
        logger.debug("\(message, privacy: .public)")
    }

    /// 信息级别日志
    /// 用于一般的信息记录
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        guard shouldLog(level: .info) else { return }
        logger.info("\(message, privacy: .public)")
    }

    /// 通知级别日志
    /// 用于重要的状态变化或事件
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func notice(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        guard shouldLog(level: .notice) else { return }
        logger.notice("\(message, privacy: .public)")
    }

    /// 警告级别日志
    /// 用于可能导致问题的情况
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        guard shouldLog(level: .warning) else { return }
        logger.warning("\(message, privacy: .public)")
    }

    /// 错误级别日志
    /// 用于错误情况的记录
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        guard shouldLog(level: .error) else { return }
        logger.error("\(message, privacy: .public)")
    }

    /// 故障级别日志
    /// 用于严重的系统错误或故障
    /// - Parameters:
    ///   - message: 日志消息
    ///   - file: 文件名（自动获取）
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    func fault(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        guard shouldLog(level: .fault) else { return }
        logger.fault("\(message, privacy: .public)")
    }

    /// 获取当前日志级别
    /// - Returns: 当前日志级别
    static func getCurrentLogLevel() -> LogLevel {
        return currentLogLevel
    }

    /// 获取当前日志级别名称
    /// - Returns: 当前日志级别名称
    static func getCurrentLogLevelName() -> String {
        return currentLogLevel.name
    }
}
