import Foundation
import CoreGraphics
import UIKit

/// 输入事件类型
enum InputEventType: String {
    case input = "INPUT"   // 键盘输入
    case scroll = "SCROLL" // 滚动
    case down = "DOWN"     // 触摸按下
    case up = "UP"         // 触摸抬起
    case move = "MOVE"     // 触摸移动
}

/// 输入事件基类
protocol InputEvent {
    var type: InputEventType { get }
}

/// 键盘输入事件
struct KeyboardInputEvent: InputEvent {
    let type: InputEventType = .input
    let text: String
}

/// 滚动事件
struct ScrollEvent: InputEvent {
    let type: InputEventType = .scroll
    let deltaH: CGFloat
}

/// 触摸事件
struct TouchEvent: InputEvent {
    let type: InputEventType
    let positionX: CGFloat
    let positionY: CGFloat
}

/// 输入事件工厂
class InputEventFactory {
    static func createEvent(from data: Data) -> InputEvent? {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let typeString = json["type"] as? String,
              let eventType = InputEventType(rawValue: typeString) else {
            return nil
        }

        switch eventType {
        case .input:
            guard let word = json["word"] as? String else { return nil }
            return KeyboardInputEvent(text: word)

        case .scroll:
            guard let deltaH = json["h"] as? CGFloat else { return nil }
            return ScrollEvent(deltaH: deltaH)

        case .down, .up, .move:
            guard let posX = json["x"] as? CGFloat,
                  let posY = json["y"] as? CGFloat else { return nil }
            return TouchEvent(type: eventType, positionX: posX, positionY: posY)
        }
    }
}

// MARK: - IOKit 系统级输入处理

/// IOKit 类型定义
typealias IOHIDEventRef = UnsafeMutableRawPointer
typealias IOHIDEventSystemClientRef = UnsafeMutableRawPointer
typealias IOHIDDigitizerTransducerType = UInt32
typealias IOHIDFloat = Double
typealias IOOptionBits = UInt32
typealias IOHIDEventField = UInt32
typealias IOHIDEventOptionBits = UInt32

/// IOKit 常量定义
let kIOHIDDigitizerTransducerTypeHand: IOHIDDigitizerTransducerType = 0
let kIOHIDDigitizerTransducerTypeFinger: IOHIDDigitizerTransducerType = 22
let kIOHIDDigitizerEventRange: UInt32 = 1 << 0
let kIOHIDDigitizerEventTouch: UInt32 = 1 << 1
let kIOHIDDigitizerEventIdentity: UInt32 = 1 << 2
let kIOHIDDigitizerEventPosition: UInt32 = 1 << 3
let kIOHIDEventFieldIsBuiltIn: IOHIDEventField = 0x00010000
let kIOHIDEventFieldDigitizerIsDisplayIntegrated: IOHIDEventField = 0x00030001

/// HID 使用页面常量
let kHIDPage_KeyboardOrKeypad: UInt16 = 0x07
let kHIDPage_Consumer: UInt16 = 0x0C
let kHIDPage_Telephony: UInt16 = 0x0B

/// HID 键盘使用常量（基于你的原始代码）
let kHIDUsage_Keyboard1: UInt16 = 0x1E
let kHIDUsage_Keyboard2: UInt16 = 0x1F
let kHIDUsage_Keyboard3: UInt16 = 0x20
let kHIDUsage_Keyboard4: UInt16 = 0x21
let kHIDUsage_Keyboard5: UInt16 = 0x22
let kHIDUsage_Keyboard6: UInt16 = 0x23
let kHIDUsage_Keyboard7: UInt16 = 0x24
let kHIDUsage_Keyboard8: UInt16 = 0x25
let kHIDUsage_Keyboard9: UInt16 = 0x26
let kHIDUsage_Keyboard0: UInt16 = 0x27

let kHIDUsage_KeyboardA: UInt16 = 0x04
let kHIDUsage_KeyboardB: UInt16 = 0x05
let kHIDUsage_KeyboardC: UInt16 = 0x06
let kHIDUsage_KeyboardD: UInt16 = 0x07
let kHIDUsage_KeyboardE: UInt16 = 0x08
let kHIDUsage_KeyboardF: UInt16 = 0x09
let kHIDUsage_KeyboardG: UInt16 = 0x0A
let kHIDUsage_KeyboardH: UInt16 = 0x0B
let kHIDUsage_KeyboardI: UInt16 = 0x0C
let kHIDUsage_KeyboardJ: UInt16 = 0x0D
let kHIDUsage_KeyboardK: UInt16 = 0x0E
let kHIDUsage_KeyboardL: UInt16 = 0x0F
let kHIDUsage_KeyboardM: UInt16 = 0x10
let kHIDUsage_KeyboardN: UInt16 = 0x11
let kHIDUsage_KeyboardO: UInt16 = 0x12
let kHIDUsage_KeyboardP: UInt16 = 0x13
let kHIDUsage_KeyboardQ: UInt16 = 0x14
let kHIDUsage_KeyboardR: UInt16 = 0x15
let kHIDUsage_KeyboardS: UInt16 = 0x16
let kHIDUsage_KeyboardT: UInt16 = 0x17
let kHIDUsage_KeyboardU: UInt16 = 0x18
let kHIDUsage_KeyboardV: UInt16 = 0x19
let kHIDUsage_KeyboardW: UInt16 = 0x1A
let kHIDUsage_KeyboardX: UInt16 = 0x1B
let kHIDUsage_KeyboardY: UInt16 = 0x1C
let kHIDUsage_KeyboardZ: UInt16 = 0x1D

let kHIDUsage_KeyboardReturnOrEnter: UInt16 = 0x28
let kHIDUsage_KeyboardDeleteOrBackspace: UInt16 = 0x2A
let kHIDUsage_KeyboardTab: UInt16 = 0x2B
let kHIDUsage_KeyboardSpacebar: UInt16 = 0x2C
let kHIDUsage_KeyboardHyphen: UInt16 = 0x2D
let kHIDUsage_KeyboardEqualSign: UInt16 = 0x2E
let kHIDUsage_KeyboardOpenBracket: UInt16 = 0x2F
let kHIDUsage_KeyboardCloseBracket: UInt16 = 0x30
let kHIDUsage_KeyboardBackslash: UInt16 = 0x31
let kHIDUsage_KeyboardSemicolon: UInt16 = 0x33
let kHIDUsage_KeyboardQuote: UInt16 = 0x34
let kHIDUsage_KeyboardGraveAccentAndTilde: UInt16 = 0x35
let kHIDUsage_KeyboardComma: UInt16 = 0x36
let kHIDUsage_KeyboardPeriod: UInt16 = 0x37
let kHIDUsage_KeyboardSlash: UInt16 = 0x38

let kHIDUsage_KeyboardLeftShift: UInt16 = 0xE1
let kHIDUsage_KeyboardRightShift: UInt16 = 0xE5
let kHIDUsage_KeyboardLeftControl: UInt16 = 0xE0
let kHIDUsage_KeyboardRightControl: UInt16 = 0xE4
let kHIDUsage_KeyboardLeftAlt: UInt16 = 0xE2
let kHIDUsage_KeyboardRightAlt: UInt16 = 0xE6
let kHIDUsage_KeyboardLeftGUI: UInt16 = 0xE3
let kHIDUsage_KeyboardRightGUI: UInt16 = 0xE7

let kHIDUsage_KeyboardUpArrow: UInt16 = 0x52
let kHIDUsage_KeyboardDownArrow: UInt16 = 0x51
let kHIDUsage_KeyboardLeftArrow: UInt16 = 0x50
let kHIDUsage_KeyboardRightArrow: UInt16 = 0x4F
let kHIDUsage_KeyboardHome: UInt16 = 0x4A
let kHIDUsage_KeyboardEnd: UInt16 = 0x4D
let kHIDUsage_KeyboardPageUp: UInt16 = 0x4B
let kHIDUsage_KeyboardPageDown: UInt16 = 0x4E

/// Consumer 页面使用常量
let kHIDUsage_Csmr_Menu: UInt16 = 0x40
let kHIDUsage_Csmr_Power: UInt16 = 0x30

/// Telephony 页面使用常量
let kHIDUsage_Tfon_Flash: UInt16 = 0x20

/// IOKit 函数声明
@_silgen_name("IOHIDEventSystemClientCreate")
func IOHIDEventSystemClientCreate(_ allocator: CFAllocator?) -> IOHIDEventSystemClientRef?

@_silgen_name("IOHIDEventSystemClientDispatchEvent")
func IOHIDEventSystemClientDispatchEvent(_ client: IOHIDEventSystemClientRef, _ event: IOHIDEventRef)

@_silgen_name("IOHIDEventCreateDigitizerEvent")
func IOHIDEventCreateDigitizerEvent(_ allocator: CFAllocator?, _ timeStamp: UInt64, _ type: IOHIDDigitizerTransducerType, _ index: UInt32, _ identity: UInt32, _ eventMask: UInt32, _ buttonMask: UInt32, _ x: IOHIDFloat, _ y: IOHIDFloat, _ z: IOHIDFloat, _ tipPressure: IOHIDFloat, _ barrelPressure: IOHIDFloat, _ range: Bool, _ touch: Bool, _ options: IOOptionBits) -> IOHIDEventRef?

@_silgen_name("IOHIDEventCreateDigitizerFingerEvent")
func IOHIDEventCreateDigitizerFingerEvent(_ allocator: CFAllocator?, _ timeStamp: UInt64, _ index: UInt32, _ identity: UInt32, _ eventMask: UInt32, _ x: IOHIDFloat, _ y: IOHIDFloat, _ z: IOHIDFloat, _ tipPressure: IOHIDFloat, _ twist: IOHIDFloat, _ range: Bool, _ touch: Bool, _ options: IOOptionBits) -> IOHIDEventRef?

@_silgen_name("IOHIDEventCreateKeyboardEvent")
func IOHIDEventCreateKeyboardEvent(_ allocator: CFAllocator?, _ time: UInt64, _ page: UInt16, _ usage: UInt16, _ down: Bool, _ flags: IOHIDEventOptionBits) -> IOHIDEventRef?

@_silgen_name("IOHIDEventAppendEvent")
func IOHIDEventAppendEvent(_ parent: IOHIDEventRef, _ child: IOHIDEventRef)

@_silgen_name("IOHIDEventSetIntegerValue")
func IOHIDEventSetIntegerValue(_ event: IOHIDEventRef, _ field: IOHIDEventField, _ value: Int32)

@_silgen_name("IOHIDEventSetSenderID")
func IOHIDEventSetSenderID(_ event: IOHIDEventRef, _ sender: UInt64)

@_silgen_name("mach_absolute_time")
func mach_absolute_time() -> UInt64

/// 系统级输入事件处理器（单例）
class SystemInputHandler {
    static let shared = SystemInputHandler()

    private let logger = DaLog(subsystem: "com.iantsrtc.input", category: "SystemInputHandler")
    private var hidClient: IOHIDEventSystemClientRef?
    private let screenSize: CGSize

    // 触摸状态跟踪
    private var isCurrentlyTouching = false
    private var lastTouchX: CGFloat = 0
    private var lastTouchY: CGFloat = 0

    private init() {
        // 获取实际屏幕尺寸（像素级别）
        let screenBounds = UIScreen.main.bounds
        let screenScale = UIScreen.main.scale
        self.screenSize = CGSize(
            width: screenBounds.width * screenScale,
            height: screenBounds.height * screenScale
        )

        // 初始化 HID 客户端
        self.hidClient = IOHIDEventSystemClientCreate(kCFAllocatorDefault)

        logger.info("🎮 系统输入处理器初始化完成")
        logger.info("   实际屏幕尺寸: \(Int(screenSize.width))x\(Int(screenSize.height)) 像素")
        logger.info("   坐标系统: 左上角(0,0) -> 右下角(\(screenSize.width),\(screenSize.height))")
        logger.info("   HID客户端状态: \(hidClient != nil ? "✅ 成功创建" : "❌ 创建失败")")

        if hidClient == nil {
            logger.error("❌ IOHIDEventSystemClientCreate 返回 nil，可能需要特殊权限")
        }
    }

    /// 处理输入事件（主要入口方法）
    func handleInputEvent(jsonData: Data) {
        guard let inputEvent = InputEventFactory.createEvent(from: jsonData) else {
            logger.warning("⚠️ 无法解析输入事件")
            return
        }

        switch inputEvent.type {
        case .input:
            if let keyboardEvent = inputEvent as? KeyboardInputEvent {
                handleKeyboardInput(keyboardEvent)
            }
        case .scroll:
            if let scrollEvent = inputEvent as? ScrollEvent {
                handleScrollInput(scrollEvent)
            }
        case .down, .up, .move:
            if let touchEvent = inputEvent as? TouchEvent {
                handleTouchInput(touchEvent)
            }
        }
    }

    /// 处理键盘输入
    private func handleKeyboardInput(_ event: KeyboardInputEvent) {
        logger.debug("📝 键盘输入: \(event.text)")

        // 遍历文本中的每个字符，转换为按键事件
        for char in event.text {
            if let usage = mapCharacterToKeyUsage(char) {
                sendKeyboardEvent(usage: usage, isDown: true)
                sendKeyboardEvent(usage: usage, isDown: false)
            } else {
                logger.warning("⚠️ 无法映射字符: \(char)")
            }
        }
    }

    /// 将字符映射到HID键盘使用码（基于你的原始VNCKeyboard实现）
    private func mapCharacterToKeyUsage(_ char: Character) -> UInt16? {
        switch char {
        // 数字键
        case "1", "!": return kHIDUsage_Keyboard1
        case "2", "@": return kHIDUsage_Keyboard2
        case "3", "#": return kHIDUsage_Keyboard3
        case "4", "$": return kHIDUsage_Keyboard4
        case "5", "%": return kHIDUsage_Keyboard5
        case "6", "^": return kHIDUsage_Keyboard6
        case "7", "&": return kHIDUsage_Keyboard7
        case "8", "*": return kHIDUsage_Keyboard8
        case "9", "(": return kHIDUsage_Keyboard9
        case "0", ")": return kHIDUsage_Keyboard0

        // 字母键（大小写）
        case "A", "a": return kHIDUsage_KeyboardA
        case "B", "b": return kHIDUsage_KeyboardB
        case "C", "c": return kHIDUsage_KeyboardC
        case "D", "d": return kHIDUsage_KeyboardD
        case "E", "e": return kHIDUsage_KeyboardE
        case "F", "f": return kHIDUsage_KeyboardF
        case "G", "g": return kHIDUsage_KeyboardG
        case "H", "h": return kHIDUsage_KeyboardH
        case "I", "i": return kHIDUsage_KeyboardI
        case "J", "j": return kHIDUsage_KeyboardJ
        case "K", "k": return kHIDUsage_KeyboardK
        case "L", "l": return kHIDUsage_KeyboardL
        case "M", "m": return kHIDUsage_KeyboardM
        case "N", "n": return kHIDUsage_KeyboardN
        case "O", "o": return kHIDUsage_KeyboardO
        case "P", "p": return kHIDUsage_KeyboardP
        case "Q", "q": return kHIDUsage_KeyboardQ
        case "R", "r": return kHIDUsage_KeyboardR
        case "S", "s": return kHIDUsage_KeyboardS
        case "T", "t": return kHIDUsage_KeyboardT
        case "U", "u": return kHIDUsage_KeyboardU
        case "V", "v": return kHIDUsage_KeyboardV
        case "W", "w": return kHIDUsage_KeyboardW
        case "X", "x": return kHIDUsage_KeyboardX
        case "Y", "y": return kHIDUsage_KeyboardY
        case "Z", "z": return kHIDUsage_KeyboardZ

        // 特殊字符
        case "_", "-": return kHIDUsage_KeyboardHyphen
        case "+", "=": return kHIDUsage_KeyboardEqualSign
        case "{", "[": return kHIDUsage_KeyboardOpenBracket
        case "}", "]": return kHIDUsage_KeyboardCloseBracket
        case "|", "\\": return kHIDUsage_KeyboardBackslash
        case ":", ";": return kHIDUsage_KeyboardSemicolon
        case "\"", "'": return kHIDUsage_KeyboardQuote
        case "~", "`": return kHIDUsage_KeyboardGraveAccentAndTilde
        case "<", ",": return kHIDUsage_KeyboardComma
        case ">", ".": return kHIDUsage_KeyboardPeriod
        case "?", "/": return kHIDUsage_KeyboardSlash

        // 控制字符
        case "\n", "\r": return kHIDUsage_KeyboardReturnOrEnter
        case "\t": return kHIDUsage_KeyboardTab
        case " ": return kHIDUsage_KeyboardSpacebar

        default:
            return nil
        }
    }

    /// 发送键盘事件
    private func sendKeyboardEvent(usage: UInt16, isDown: Bool) {
        guard let event = IOHIDEventCreateKeyboardEvent(
            kCFAllocatorDefault,
            mach_absolute_time(),
            kHIDPage_KeyboardOrKeypad,
            usage,
            isDown,
            0
        ) else {
            logger.error("❌ 创建键盘事件失败")
            return
        }

        sendHIDEvent(event)
    }

    /// 处理滚动事件
    private func handleScrollInput(_ event: ScrollEvent) {
        logger.debug("📜 滚动: \(event.deltaH)")

        // 根据滚动方向发送相应的按键事件
        // 正值向下滚动，负值向上滚动
        if event.deltaH > 0 {
            // 向下滚动 - 发送向下箭头键
            logger.debug("📜 向下滚动")
            sendKeyboardEvent(usage: kHIDUsage_KeyboardDownArrow, isDown: true)
            sendKeyboardEvent(usage: kHIDUsage_KeyboardDownArrow, isDown: false)
        } else if event.deltaH < 0 {
            // 向上滚动 - 发送向上箭头键
            logger.debug("📜 向上滚动")
            sendKeyboardEvent(usage: kHIDUsage_KeyboardUpArrow, isDown: true)
            sendKeyboardEvent(usage: kHIDUsage_KeyboardUpArrow, isDown: false)
        }

        // 也可以根据原始代码发送特殊的按钮事件（如果需要的话）
        // 这里实现了基本的滚动功能
    }

    /// 处理触摸事件
    private func handleTouchInput(_ event: TouchEvent) {
        // 传入的坐标已经是归一化坐标(0.0-1.0)，直接使用！
        let normalizedX = event.positionX
        let normalizedY = event.positionY

        // 计算像素坐标用于日志显示
        let pixelX = normalizedX * screenSize.width
        let pixelY = normalizedY * screenSize.height

        logger.debug("👆 触摸\(event.type.rawValue): 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY))) -> 像素(\(Int(pixelX)), \(Int(pixelY)))")

        switch event.type {
        case .down:
            logger.info("🔽 触摸按下: 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY)))")
            sendTouchEventOCStyle(normalizedX: normalizedX, normalizedY: normalizedY, isTouch: true, isFirstTouch: true)
            isCurrentlyTouching = true
            lastTouchX = normalizedX
            lastTouchY = normalizedY

        case .up:
            logger.info("🔼 触摸抬起: 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY)))")
            sendTouchEventOCStyle(normalizedX: normalizedX, normalizedY: normalizedY, isTouch: false, isFirstTouch: false)
            isCurrentlyTouching = false

        case .move:
            if isCurrentlyTouching {
                logger.debug("↔️ 触摸移动: 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY)))")
                sendTouchEventOCStyle(normalizedX: normalizedX, normalizedY: normalizedY, isTouch: true, isFirstTouch: false)
                lastTouchX = normalizedX
                lastTouchY = normalizedY
            } else {
                logger.warning("⚠️ 收到移动事件但当前未在触摸状态")
            }

        default:
            break
        }
    }

    /// 发送触摸事件到系统 - 完全按照原始OC版本实现
    private func sendTouchEventOCStyle(normalizedX: CGFloat, normalizedY: CGFloat, isTouch: Bool, isFirstTouch: Bool) {
        guard hidClient != nil else {
            logger.error("❌ HID客户端未初始化，无法发送触摸事件")
            return
        }

        let timestamp = mach_absolute_time()
        logger.debug("🔧 准备发送触摸事件(OC风格): 坐标(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY))), 触摸: \(isTouch), 首次: \(isFirstTouch)")

        // 完全按照原始OC版本的事件掩码逻辑
        var handEventMask: UInt32 = 0
        var fingerEventMask: UInt32 = 0
        var eventDescription = ""

        let twas = isCurrentlyTouching  // 之前是否在触摸
        let tis = isTouch              // 现在是否在触摸

        if !twas && tis {
            // 开始触摸 - 与OC版本完全一致
            handEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch | kIOHIDDigitizerEventIdentity
            fingerEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch
            eventDescription = "开始触摸"
        } else if twas && tis {
            // 移动触摸 - 与OC版本完全一致
            handEventMask = kIOHIDDigitizerEventPosition
            fingerEventMask = kIOHIDDigitizerEventPosition
            eventDescription = "移动触摸"
        } else if twas && !tis {
            // 结束触摸 - 与OC版本完全一致
            handEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch | kIOHIDDigitizerEventIdentity | kIOHIDDigitizerEventPosition
            fingerEventMask = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch
            eventDescription = "结束触摸"
        } else {
            // 无效状态，直接返回
            return
        }

        logger.debug("🔧 事件类型: \(eventDescription), 手部掩码: 0x\(String(handEventMask, radix: 16)), 手指掩码: 0x\(String(fingerEventMask, radix: 16))")

        // 创建手部事件 - 与OC版本完全一致的参数
        logger.debug("🔧 正在创建手部事件...")
        guard let handEvent = IOHIDEventCreateDigitizerEvent(
            kCFAllocatorDefault,
            timestamp,
            kIOHIDDigitizerTransducerTypeHand,  // 0 - 与OC版本一致
            1 << 22,  // index: 4194304 - 与OC版本一致
            1,        // identity: 1 - 与OC版本一致
            handEventMask,
            0,        // buttonMask: 0 - 与OC版本一致
            normalizedX,
            normalizedY,
            0,        // z: 0 - 与OC版本一致
            0,        // tipPressure: 0 - 与OC版本一致
            0,        // barrelPressure: 0 - 与OC版本一致
            false,    // range: false - 与OC版本一致
            false,    // touch: false - 与OC版本一致（手部事件）
            0         // options: 0 - 与OC版本一致
        ) else {
            logger.error("❌ IOHIDEventCreateDigitizerEvent 返回 nil")
            return
        }
        logger.debug("✅ 手部事件创建成功")

        // 设置事件属性 - 与OC版本一致
        logger.debug("🔧 设置手部事件属性...")
        IOHIDEventSetIntegerValue(handEvent, kIOHIDEventFieldIsBuiltIn, 1)
        IOHIDEventSetIntegerValue(handEvent, kIOHIDEventFieldDigitizerIsDisplayIntegrated, 1)

        // 创建手指事件 - 与OC版本完全一致的参数
        logger.debug("🔧 正在创建手指事件...")
        guard let fingerEvent = IOHIDEventCreateDigitizerFingerEvent(
            kCFAllocatorDefault,
            timestamp,
            3,        // index: 3 - 与OC版本一致
            2,        // identity: 2 - 与OC版本一致
            fingerEventMask,
            normalizedX,
            normalizedY,
            0,        // z: 0 - 与OC版本一致
            0,        // tipPressure: 0 - 与OC版本一致
            0,        // twist: 0 - 与OC版本一致
            tis,      // range: tis - 与OC版本一致
            tis,      // touch: tis - 与OC版本一致
            0         // options: 0 - 与OC版本一致
        ) else {
            logger.error("❌ IOHIDEventCreateDigitizerFingerEvent 返回 nil")
            return
        }
        logger.debug("✅ 手指事件创建成功")

        // 将手指事件附加到手部事件 - 与OC版本一致
        logger.debug("🔧 附加手指事件到手部事件...")
        IOHIDEventAppendEvent(handEvent, fingerEvent)

        // 发送手部事件（包含手指事件）
        logger.debug("🔧 发送HID事件到系统...")
        sendHIDEventWithSenderID(handEvent)
        logger.debug("✅ 触摸事件发送完成")

        // 添加额外的调试信息
        logger.info("📊 触摸事件摘要(OC风格): \(eventDescription) at (\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY)))")
    }

    /// 发送 HID 事件到系统（不设置 Sender ID）
    private func sendHIDEvent(_ event: IOHIDEventRef) {
        guard let client = hidClient else {
            logger.error("❌ HID客户端未初始化，无法发送事件")
            return
        }

        // 不设置发送者ID，让系统自动处理
        logger.debug("🔧 调用 IOHIDEventSystemClientDispatchEvent...")
        // 分发事件
        IOHIDEventSystemClientDispatchEvent(client, event)

        logger.debug("✅ HID事件已分发到系统")

        // 释放事件
        // CFRelease(event) // Swift 会自动管理内存
    }

    /// 发送 HID 事件到系统（设置固定的 Sender ID，与OC版本一致）
    private func sendHIDEventWithSenderID(_ event: IOHIDEventRef) {
        guard let client = hidClient else {
            logger.error("❌ HID客户端未初始化，无法发送事件")
            return
        }

        logger.debug("🔧 设置发送者ID（与OC版本一致）...")
        // 设置发送者ID（与原始OC代码完全一致）
        IOHIDEventSetSenderID(event, 0x8000000817319372)

        logger.debug("🔧 调用 IOHIDEventSystemClientDispatchEvent...")
        // 分发事件
        IOHIDEventSystemClientDispatchEvent(client, event)

        logger.debug("✅ HID事件已分发到系统")

        // 释放事件
        // CFRelease(event) // Swift 会自动管理内存
    }
}
