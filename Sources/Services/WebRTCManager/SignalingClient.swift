// SignalingClient.swift
import Foundation
import Network
import WebRTC
import os

/// 信令客户端委托协议
protocol SignalingClientDelegate: AnyObject, Sendable {
    func signalingClient(_ client: SignalingClient, didReceiveMessage message: SignalingMessage)
    func signalingClientDidConnect(_ client: SignalingClient)
    func signalingClientDidDisconnect(_ client: SignalingClient)
}

/// 被动连接的信令客户端
actor SignalingClient {
    static let shared = SignalingClient()
    
    nonisolated(unsafe) weak var delegate: SignalingClientDelegate?
    
    // MARK: - WebSocket 连接管理
    
    private let url: URL
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession
    private var _isConnected = false

    private var rtcConfig: RTCConfig
    
    // MARK: - 设备状态

    /// 设备注册状态
    private var isDeviceRegistered = false
    
    // MARK: - 连接状态
    
    private var connectionState: ConnectionState = .disconnected
    
    // MARK: - 日志
    
    private let logger = DaLog(subsystem: "com.iantsrtc.signaling", category: "SignalingClient")
    
    // MARK: - 连接状态枚举
    
    enum ConnectionState {
        case disconnected
        case connecting
        case connected
    }
    
    // MARK: - 初始化
    
    private init() {
        // 从配置或环境变量获取 URL
        self.rtcConfig = RTCConfig.default()
        // logger.debug("signalingServerUrl: \(rtcConfig.signalingServerUrl),\(rtcConfig.signalingServerUrl.absoluteString)")
        self.url = rtcConfig.signalingServerUrl
        self.urlSession = URLSession(configuration: .default)
        
        logger.info("🔧 SignalingClient 被动初始化完成")
    }
    
    // MARK: - 连接管理
    
    /// 连接到服务器
    func connect() async throws {
        guard connectionState == .disconnected else {
            logger.warning("⚠️ 连接状态不正确，当前状态: \(connectionState)")
            return
        }
        
        connectionState = .connecting
        logger.info("🔗 开始连接信令服务器...")
        
        do {
            // 创建 WebSocket 任务
            let webSocketTask = urlSession.webSocketTask(with: url)
            self.webSocketTask = webSocketTask
            
            // 开始连接
            webSocketTask.resume()
            
            // 等待连接建立
            try await waitForConnectionEstablished()
            
            // 连接成功
            _isConnected = true
            connectionState = .connected
            
            // 开始接收消息
            Task {
                await receiveMessage()
            }

            // 启动连接监控
            monitorConnection()

            // 发送 ping 测试连接
            await testConnection()

            // 通知委托
            delegate?.signalingClientDidConnect(self)

            logger.info("✅ 信令服务器连接成功")
            
        } catch {
            connectionState = .disconnected
            logger.error("❌ 连接失败: \(error)")
            throw error
        }
    }
    
    /// 断开连接
    func disconnect() async {
        logger.info("🔌 主动断开连接")
        
        connectionState = .disconnected
        _isConnected = false
        isDeviceRegistered = false
        
        // 关闭 WebSocket
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
    }
    
    /// 等待连接建立
    private func waitForConnectionEstablished() async throws {
        // 等待 1 秒让连接建立
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
    }

    /// 测试连接是否可用
    private func testConnection() async {
        guard let task = webSocketTask else {
            logger.error("❌ 无法测试连接：WebSocketTask为空")
            return
        }

        do {
            logger.debug("🏓 发送 ping 测试连接...")
            try await task.send(.string("ping"))
            logger.debug("✅ ping 发送成功")
        } catch {
            logger.error("❌ ping 发送失败: \(error)")
        }
    }
    
    // MARK: - 消息发送
    
    /// 发送信令消息
    func sendMessage(_ message: SignalingMessage) async {
        // 详细检查连接状态
        logger.debug("🔍 [发送检查] WebSocket连接状态: _isConnected=\(_isConnected)")
        logger.debug("🔍 [发送检查] WebSocketTask状态: \(webSocketTask?.state.rawValue ?? -1)")

        guard _isConnected else {
            logger.warning("⚠️ WebSocket未连接，无法发送消息")
            return
        }

        guard let task = webSocketTask else {
            logger.error("❌ WebSocketTask为空，无法发送消息")
            return
        }

        // 检查 WebSocketTask 的实际状态
        switch task.state {
        case .running:
            logger.debug("✅ WebSocketTask状态正常: running")
        case .suspended:
            logger.warning("⚠️ WebSocketTask状态: suspended")
        case .canceling:
            logger.error("❌ WebSocketTask状态: canceling")
            return
        case .completed:
            logger.error("❌ WebSocketTask状态: completed")
            return
        @unknown default:
            logger.warning("❓ WebSocketTask状态: unknown(\(task.state.rawValue))")
        }

        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(message)
            let jsonString = String(data: data, encoding: .utf8) ?? "无法解析JSON"

            logger.debug("📤 [SignalingClient] 发送消息类型: \(message.type)")
            logger.debug("📤 [SignalingClient] 发送消息完整内容:\n\(jsonString)")
            logger.debug("📤 [SignalingClient] 消息大小: \(data.count) 字节")
            logger.debug("📤 [SignalingClient] 准备通过WebSocket发送...")

            try await task.send(.data(data))
            logger.debug("✅ [SignalingClient] WebSocket.send()调用成功: \(message.type)")

            // 验证发送后的状态
            logger.debug("📋 [发送后检查] WebSocketTask状态: \(task.state.rawValue)")

        } catch {
            logger.error("❌ 发送消息失败: \(error)")
            logger.error("❌ 错误详情: \(error.localizedDescription)")

            // 检查是否是网络错误
            if let urlError = error as? URLError {
                logger.error("❌ URLError代码: \(urlError.code.rawValue)")
                logger.error("❌ URLError描述: \(urlError.localizedDescription)")
            }
        }
    }
    
    /// 发送 Offer
    func sendOffer(_ sdp: String, to targetID: String) async {
        logger.debug("📤 准备发送Offer:")
        logger.debug("   - targetID: \(targetID)")
        logger.debug("   - SDP长度: \(sdp.count) 字符")
        logger.debug("   - SDP内容前100字符: \(String(sdp.prefix(100)))")

        let offerData = WebRTCOfferData(type: "offer", sdp: sdp)
        let message = SignalingMessage(
            type: SignalingMessageType.offer,
            targetID: targetID,
            sourceID: nil,
            data: .webrtcOffer(offerData),
            message: nil,
            success: nil,
            error: nil
        )
        await sendMessage(message)
    }
    
    /// 发送 Answer
    func sendAnswer(_ sdp: String, to targetID: String) async {
        let answerData = WebRTCAnswerData(type: "answer", sdp: sdp)
        let message = SignalingMessage(
            type: SignalingMessageType.answer,
            targetID: targetID,
            sourceID: nil,
            data: .webrtcAnswer(answerData),
            message: nil,
            success: nil,
            error: nil
        )
        await sendMessage(message)
    }
    
    /// 发送 ICE 候选
    func sendIceCandidate(_ candidate: RTCIceCandidate, to targetID: String) async {
        logger.debug("🧊 准备发送ICE候选:")
        logger.debug("   - sdpMLineIndex: \(candidate.sdpMLineIndex)")
        logger.debug("   - sdpMid: \(candidate.sdpMid ?? "nil")")
        logger.debug("   - candidate: \(candidate.sdp)")
        logger.debug("   - targetID: \(targetID)")

        let candidateData = IceCandidateData(
            sdpMLineIndex: Int(candidate.sdpMLineIndex),
            candidate: candidate.sdp,
            sdpMid: candidate.sdpMid ?? ""
        )
        let message = SignalingMessage(
            type: SignalingMessageType.iceCandidate,
            targetID: targetID,
            sourceID: nil,
            data: .iceCandidate(candidateData),
            message: nil,
            success: nil,
            error: nil
        )
        await sendMessage(message)
    }
    
    // MARK: - 消息接收
    
    /// 接收消息
    private func receiveMessage() async {
        guard _isConnected else { return }
        
        webSocketTask?.receive { [weak self] result in
            guard let self = self else { return }
            
            Task {
                switch result {
                case .success(let message):
                    await self.handleReceivedMessage(message)
                    
                    // 继续接收下一条消息
                    await self.receiveMessage()
                    
                case .failure(let error):
                    await self.handleReceiveError(error)
                }
            }
        }
    }
    
    /// 处理接收到的消息
    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            await processReceivedData(data)
        case .string(let text):
            if let data = text.data(using: .utf8) {
                await processReceivedData(data)
            }
        @unknown default:
            break
        }
    }
    
    /// 处理接收到的数据
    private func processReceivedData(_ data: Data) async {
        do {
            // 记录接收到的原始数据
            if let jsonString = String(data: data, encoding: .utf8) {
                logger.debug("📥 收到原始消息数据:\n\(jsonString)")
            } else {
                logger.debug("📥 收到二进制数据，长度: \(data.count) 字节")
            }

            let message = try JSONDecoder().decode(SignalingMessage.self, from: data)
            await handleSignalingMessage(message)
        } catch {
            logger.error("❌ 解析消息失败: \(error)")
            if let jsonString = String(data: data, encoding: .utf8) {
                logger.error("❌ 原始数据: \(jsonString)")
            }
        }
    }
    
    /// 处理信令消息
    private func handleSignalingMessage(_ message: SignalingMessage) async {
        logger.debug("📥 收到消息: \(message.type)")

        // SignalingClient 只处理自己相关的消息
        switch message.type {
        case SignalingMessageType.socketOpen:
            logger.info("🔓 Socket 打开")

        case SignalingMessageType.socketMessage:
            logger.info("💬 Socket 消息: \(message.message ?? "")")

        case SignalingMessageType.socketClose:
            logger.info("🔒 Socket 关闭")

        case SignalingMessageType.register:
            await handleRegisterMessage(message)

        case SignalingMessageType.error:
            logger.error("❌ 收到错误消息: \(message.error ?? "")")

        case SignalingMessageType.execResult:
            logger.info("📋 执行结果: \(message.message ?? "")")

        default:
            // 其他业务消息转发给委托处理，避免重复处理
            delegate?.signalingClient(self, didReceiveMessage: message)
        }
    }
    
    /// 处理注册消息
    private func handleRegisterMessage(_ message: SignalingMessage) async {
        if let success = message.success, success {
            isDeviceRegistered = true
            logger.info("✅ 设备注册成功")
            
            if let msg = message.message {
                logger.info("📝 注册信息: \(msg)")
            }
        } else {
            logger.error("❌ 设备注册失败: \(message.error ?? "")")
        }
    }

    
    /// 处理接收错误
    private func handleReceiveError(_ error: Error) async {
        logger.error("❌ 接收消息失败: \(error)")
        await disconnect()
        delegate?.signalingClientDidDisconnect(self)
    }
    
    // MARK: - 状态查询
    
    /// 获取连接状态
    var isConnected: Bool {
        get async { _isConnected }
    }
    
    /// 获取连接状态
    func getConnectionStatus() -> [String: Any] {
        return [
            "isConnected": _isConnected,
            "isRegistered": isDeviceRegistered,
            "connectionState": String(describing: connectionState)
        ]
    }
    
    /// 监控连接状态
    private func monitorConnection() {
        Task {
            while webSocketTask != nil {
                try? await Task.sleep(nanoseconds: 10_000_000_000) // 10秒检查一次

                if let task = webSocketTask {
                    logger.debug("🔍 [连接监控] WebSocket状态: \(task.state.rawValue), 内部连接状态: \(_isConnected)")

                    switch task.state {
                    case .running:
                        if !_isConnected {
                            logger.warning("🔄 WebSocket状态: running，但内部状态为未连接")
                        } else {
                            logger.debug("✅ WebSocket状态正常: running & connected")
                        }
                    case .suspended:
                        logger.warning("⚠️ WebSocket状态: suspended")
                        _isConnected = false
                    case .canceling:
                        logger.warning("⚠️ WebSocket状态: canceling")
                        _isConnected = false
                    case .completed:
                        logger.warning("⚠️ WebSocket状态: completed，连接已断开")
                        _isConnected = false
                        delegate?.signalingClientDidDisconnect(self)
                        return // 退出监控循环
                    @unknown default:
                        logger.warning("⚠️ WebSocket状态: unknown(\(task.state.rawValue))")
                    }
                } else {
                    logger.warning("⚠️ WebSocketTask为空，停止监控")
                    return
                }
            }
            logger.info("🔚 连接监控已停止")
        }
    }

    deinit {
        logger.info("♻️ SignalingClient 析构")
        // 同步清理连接
        webSocketTask?.cancel()
    }
}
