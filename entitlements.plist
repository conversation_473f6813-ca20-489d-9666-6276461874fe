<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>com.apple.security.iokit-user-client-class</key>
	<array>
		<string>IOSurfaceAcceleratorClient</string>
		<string>IOMobileFramebufferUserClient</string>
		<string>IOSurfaceRootUserClient</string>
		<string>IOHIDLibUserClient</string>
	</array>
	<key>platform-application</key>
	<true/>
	<key>com.apple.private.security.no-container</key>
	<true/>
    <key>com.apple.private.security.container-required</key>
    <false/>
	<key>com.apple.private.security.container-manager</key>
	<true/>
	<key>com.apple.private.tcc.allow</key>
	<array>
		<string>kTCCServiceMicrophone</string>
	</array>
	<key>com.apple.private.tcc.manager.check-by-audit-token</key>
	<array>
		<string>kTCCServiceMicrophone</string>
	</array>
	<key>com.apple.private.skip-library-validation</key>
	<true/>
	<key>run-unsigned-code</key>
	<true/>
	<key>get-task-allow</key>
	<true/>
	<key>com.apple.private.hid.client.event-dispatch</key>
	<true/>
	<key>com.apple.private.hid.client.event-filter</key>
	<true/>
	<key>com.apple.private.hid.client.event-monitor</key>
	<true/>
	<key>com.apple.private.hid.client.service-protected</key>
	<true/>
	<key>com.apple.private.IOSurface.protected-access</key>
	<true/>
	<key>com.apple.QuartzCore.displayable-context</key>
	<true/>
	<key>com.apple.QuartzCore.global-capture</key>
	<true/>
	<key>com.apple.QuartzCore.secure-mode</key>
	<true/>
	<key>com.apple.gasgauge.user-access-device</key>
	<true/>
	<key>com.apple.private.allow-explicit-graphics-priority</key>
	<true/>
	<key>com.apple.CommCenter.fine-grained</key>
	<array>
		<string>spi</string>
	</array>
	<key>com.apple.private.applesepmanager.allow</key>
	<true/>
    <key>com.apple.private.iokit.device-access</key>
    <true/>
	<key>com.apple.private.security.disk-device-access</key>
	<true/>

	    <!-- Metal 相关权限 -->
    <key>com.apple.private.metal</key>
    <true/>
    <key>com.apple.private.iokit.IOAccelerator</key>
    <true/>
    <key>com.apple.private.iokit.IOGPUFamily</key>
    <true/>
	    <!-- IOKit 访问权限 -->
    <key>com.apple.private.iokit.user-access</key>
    <true/>
    <key>com.apple.private.iokit</key>
    <array>
        <string>IOAccelerator</string>
        <string>IOGPUDevice</string>
        <string>IOSurface</string>
    </array>
	<key>com.apple.private.xpc.launchd.domain</key>
    <string>system</string>
	<key>task_for_pid-allow</key>
    <true/>

	<!-- 图形渲染权限 -->
	<key>com.apple.QuartzCore.secure-capture</key>
	<true/>
	<key>com.apple.QuartzCore.system-layers</key>
	<true/>
	<key>com.apple.QuartzCore.displayable-context</key>
	<true/>

	<!-- 硬件访问权限 -->
	<key>com.apple.backboard.client</key>
	<true/>
	<key>com.apple.backboard.display.archive</key>
	<true/>

	<!-- 多媒体处理权限 -->
	<key>com.apple.coremedia.allow-pre-wiring-pixel-buffers</key>
	<true/>
	<key>com.apple.coremedia.virtualdisplaysession</key>
	<true/>

</dict>
</plist>