//
//  LogC.h
//  iAntsCore
//
//  Created by sun on 2024/10/18.
//

#ifndef LogC_h
#define LogC_h

#import <Foundation/Foundation.h>

// 日志等级枚举
typedef NS_ENUM(NSUInteger, LogLevel) {
    LogLevelError = 0, // 错误日志
    LogLevelWarning,   // 警告日志
    LogLevelInfo,      // 信息日志
    LogLevelDebug,     // 调试日志（仅在 DEBUG 模式下输出）
};

#ifdef __cplusplus
extern "C" {
#endif

// 自定义日志输出函数声明
void LogMessage(LogLevel level, const char *file, int line, const char *function, NSString *format, ...);

#ifdef __cplusplus
}
#endif

// 宏定义，简化使用
#define LOGE(fmt, ...) LogMessage(LogLevelError, __FILE__, __LINE__, __PRETTY_FUNCTION__, fmt, ##__VA_ARGS__)    // 错误日志，所有环境输出
#define LOGW(fmt, ...) LogMessage(LogLevelWarning, __FILE__, __LINE__, __PRETTY_FUNCTION__, fmt, ##__VA_ARGS__)  // 警告日志，所有环境输出
#define LOGI(fmt, ...) LogMessage(LogLevelInfo, __FILE__, __LINE__, __PRETTY_FUNCTION__, fmt, ##__VA_ARGS__)     // 信息日志，所有环境输出
#define LOGD(fmt, ...) LogMessage(LogLevelDebug, __FILE__, __LINE__, __PRETTY_FUNCTION__, fmt, ##__VA_ARGS__)    // 调试日志，仅 DEBUG 环境输出

#endif /* LogC_h */
