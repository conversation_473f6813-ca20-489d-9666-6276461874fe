//
//  ScreenCapture.swift
//  iAntsRTC
//
//  屏幕采集器 - 负责从iOS系统获取屏幕内容
//  支持高帧率采集，为多个处理器提供IOSurface
//  添加：动态启停 + 全部后台分发
//

import Foundation
import UIKit
import QuartzCore
import IOSurface

protocol ScreenCaptureDelegate: AnyObject {
    func screenCaptureDidUpdateIOSurface(_ newSurface: IOSurfaceRef)
}

class ScreenCapture {

    static let shared = ScreenCapture()

    // MARK: — 日志 & 基本状态 —

    private let logger = DaLog(subsystem: "com.iantsrtc.capture", category: "ScreenCapture")
    private var currentSurface: IOSurfaceRef?
    private var framebufferConnection: UnsafeMutableRawPointer?
    private var frameCounter = 0

    // MARK: — 多播委托相关 —

    private let delegates = NSHashTable<AnyObject>.weakObjects()
    private let delegateQueue = DispatchQueue(
        label: "com.iantsrtc.screen.delegates",
        qos: .userInitiated, attributes: .concurrent
    )
    private var cachedDelegates: [ScreenCaptureDelegate] = []
    private var cacheValid = false
    private var lastCleanupTime: CFTimeInterval = 0

    // MARK: — CADisplayLink 控制 —

    private lazy var displayLink: CADisplayLink = {
        let cadl = CADisplayLink(target: self, selector: #selector(checkForUpdates))
        if #available(iOS 10.0, *) {
            cadl.preferredFramesPerSecond = 60
        } else {
            cadl.frameInterval = 1
        }
        cadl.add(to: .main, forMode: .common)
        cadl.isPaused = true    // 初始为暂停
        return cadl
    }()

    // MARK: — 初始化 & 准备工作 —

    init() {
        logger.info("ScreenCapture 初始化，准备 framebuffer/IOSurface")
        prepareIOSurface()
    }

    /// 预取 framebuffer 和 surface，只做一次
    private func prepareIOSurface() {
        var fbconn: UnsafeMutableRawPointer?
        let ret = IOMobileFramebufferGetMainDisplay(&fbconn)
        guard ret == 0, let conn = fbconn else {
            logger.error("❌ 获取 main display 失败：\(ret)")
            return
        }
        framebufferConnection = conn

        // 尝试拿默认 surface
        var surface: IOSurfaceRef?
        if IOMobileFramebufferGetLayerDefaultSurface(conn, 0, &surface) != 0 {
            // fallback
            if IOMobileFramebufferCopyLayerDisplayedSurface(conn, 0, &surface) != 0 {
                logger.error("❌ 双重尝试均无法获取 IOSurface")
                return
            }
        }
        guard let sur = surface else {
            logger.error("❌ IOSurface 仍然为 nil")
            return
        }
        currentSurface = sur
        logger.info("✅ 成功准备 IOSurface id=\(IOSurfaceGetID(sur))")
    }

    // MARK: — 动态启停采集 —

    func addDelegate(_ delegate: ScreenCaptureDelegate) {
        delegateQueue.async(flags: .barrier) { [weak self] in
            guard let self = self else { return }
            // 去重
            if !self.delegates.allObjects.contains(where: { $0 === delegate }) {
                self.delegates.add(delegate)
                self.cacheValid = false
                let count = self.delegates.count
                self.logger.info("➕ 添加代理，count=\(count)")

                // 代理数从 0→1，自动开采集
                if count == 1 {
                    DispatchQueue.main.async { self.displayLink.isPaused = false }
                    self.logger.info("▶️ 自动开始屏幕采集")
                }
            }
        }
    }

    func removeDelegate(_ delegate: ScreenCaptureDelegate) {
        delegateQueue.async(flags: .barrier) { [weak self] in
            guard let self = self else { return }
            self.delegates.remove(delegate)
            self.cacheValid = false
            let count = self.delegates.count
            self.logger.info("➖ 移除代理，count=\(count)")

            // 代理数从 1→0，自动停采集
            if count == 0 {
                DispatchQueue.main.async { self.displayLink.isPaused = true }
                self.logger.info("⏸️ 自动停止屏幕采集")
            }
        }
    }

    func getDelegateCount() -> Int {
        return delegateQueue.sync { delegates.count }
    }

    // MARK: — 多播分发（全部后台队列，无主线程跳转） —

    @objc private func checkForUpdates() {
        guard let surface = currentSurface else { return }
        frameCounter += 1

        if frameCounter % 300 == 0 {
            let id = IOSurfaceGetID(surface)
            logger.info("📸 已处理 \(frameCounter) 帧，surfaceID=\(id)，代理数=\(getDelegateCount())")
        }

        delegateQueue.async { [weak self] in
            guard let self = self else { return }
            if !self.cacheValid {
                self.cachedDelegates = self.delegates.allObjects.compactMap { $0 as? ScreenCaptureDelegate }
                self.cacheValid = true
            }
            // 逐一分发
            for delegate in self.cachedDelegates {
                delegate.screenCaptureDidUpdateIOSurface(surface)
            }
            // 定期触发 NSHashTable 内部清理
            let now = CFAbsoluteTimeGetCurrent()
            if now - self.lastCleanupTime > 5 {
                _ = self.delegates.allObjects
                self.lastCleanupTime = now
            }
        }
    }

    deinit {
        logger.info("🗑️ ScreenCapture 析构，清理")
        displayLink.invalidate()
        // delegates 会自动清理
    }
}
