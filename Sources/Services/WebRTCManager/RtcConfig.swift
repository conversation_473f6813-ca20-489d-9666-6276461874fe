import Foundation

/// WebRTC配置类，提供TURN和STUN服务器地址等配置信息
struct RTCConfig {
    /// STUN服务器地址
    let stunServers: [String]
    
    /// TURN服务器配置
    let turnServers: [TURNServer]
    
    /// 信令服务器URL
    let signalingServerUrl: URL
    
    /// 设备序列号
    let serialNumber: String
    
    /// 默认配置
    static func `default`() -> RTCConfig {
        return RTCConfig(
            stunServers: [
                "stun:114.55.114.193:13478"
            ],
            turnServers: [
                TURNServer(
                    urls: ["turn:114.55.114.193:13478"],
                    username: "dataant",
                    credential: "dataant666"
                )
            ],
            signalingServerUrl: URL(string: "ws://114.55.114.193:13477?id=\(Utils.getSerialNumber())")!,
            serialNumber: Utils.getSerialNumber()
        )
    }
}

/// TURN服务器信息
struct TURNServer {
    let urls: [String]
    let username: String
    let credential: String
}
