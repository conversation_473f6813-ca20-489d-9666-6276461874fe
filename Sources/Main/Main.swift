import Foundation
import os

// 全局日志实例
private let logger = Logger(subsystem: "com.iantsrtc.main", category: "Application")

// 使用actor来安全管理应用状态
@globalActor
actor AppState {
    static let shared = AppState()

    private var _shouldExit = false
    private var _sessionManager: Any?

    var shouldExit: Bool {
        _shouldExit
    }

    var sessionManager: Any? {
        _sessionManager
    }

    func setSessionManager(_ manager: Any) {
        _sessionManager = manager
    }

    func requestExit() {
        _shouldExit = true
    }
}

/// 信号处理函数
func handleSignal(_ signal: Int32) {
    logger.info("收到退出信号(\(signal))，正在关闭服务...")

    Task {
        await AppState.shared.requestExit()
        logger.info("服务已停止")
    }
}

/// 设置信号处理
func setupSignalHandling() {
    signal(SIGINT, handleSignal)
    signal(SIGTERM, handleSignal)
}

/// iAntsRTC - iOS远程控制服务主程序
@main
struct IAntsRTC {
    static func main() async {
        // 设置信号处理
        setupSignalHandling()

        logger.info("""
        =====================================
        🚀 iAntsRTC - iOS远程控制服务启动
        =====================================
        """)

        // 获取命令行参数
        let arguments = CommandLine.arguments

        // 检查是否有 test 参数
        if arguments.count >= 2 && arguments[1].lowercased() == "test" {
            logger.info("🧪 启动测试模式，使用 RtcSessionManager")

            // 序列号获取是否成功
            logger.info("设备序列号: \(Utils.getSerialNumber())")

            // 启动 RtcSessionManager
            let sessionManager = RtcSessionManager.shared
            await AppState.shared.setSessionManager(sessionManager)

            let result = await sessionManager.start()
            if result.success {
                logger.info("✅ RtcSessionManager 启动完成，程序正在运行...")
                logger.info("📡 等待远程连接请求...")
                logger.info("🔄 按 Ctrl+C 退出程序")

                // 保持程序运行
                while !(await AppState.shared.shouldExit) {
                    try? await Task.sleep(nanoseconds: 500_000_000) // 500ms
                }

                logger.info("程序正常退出")
            } else {
                logger.error("❌ RtcSessionManager 启动失败: \(result.error ?? "未知错误")")
                exit(1)
            }
        } else {
            logger.error("❌ 缺少必要参数")
            logger.info("用法: \(arguments[0]) test")
            logger.info("目前只支持 test 模式启动 RtcSessionManager")
            exit(1)
        }
    }
}
