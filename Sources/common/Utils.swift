import Foundation

class Utils {
    /// 获取设备序列号
    /// - Returns: 设备序列号字符串
    static func getSerialNumber() -> String {
        // 定义函数指针类型
        typealias MGCopyAnswerFunction = @convention(c) (CFString) -> CFString?

        // 动态加载 libMobileGestalt.dylib
        guard let gestalt = dlopen("/usr/lib/libMobileGestalt.dylib", RTLD_GLOBAL | RTLD_LAZY) else {
            logG.error("Failed to load libMobileGestalt.dylib")
            return "Unknown"
        }

        // 获取 MGCopyAnswer 函数
        guard let mgCopyAnswerPtr = dlsym(gestalt, "MGCopyAnswer") else {
            logG.error("Failed to find MGCopyAnswer symbol")
            dlclose(gestalt)
            return "Unknown"
        }

        // 转换为函数指针
        let mgCopyAnswer = unsafeBitCast(mgCopyAnswerPtr, to: MGCopyAnswerFunction.self)

        // 调用函数获取序列号
        guard let serialNumberCF = mgCopyAnswer("SerialNumber" as CFString) else {
            logG.error("Failed to get SerialNumber")
            dlclose(gestalt)
            return "Unknown"
        }

        // 转换为 Swift String
        let serialNumber = serialNumberCF as String

        // 清理资源
        dlclose(gestalt)

        logG.info("SerialNumber = \(serialNumber)")
        return serialNumber
    }
}
