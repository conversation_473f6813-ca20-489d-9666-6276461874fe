import Foundation
import Network

// MARK: - WebSocket Client using async/await and Actors

public actor WSClient {
    
    // MARK: - Types
    
    public enum State: Sendable, Equatable {
        case disconnected
        case connecting
        case connected
        case reconnecting
        case error(String)

        public static func == (lhs: State, rhs: State) -> Bool {
            switch (lhs, rhs) {
            case (.disconnected, .disconnected),
                 (.connecting, .connecting),
                 (.connected, .connected),
                 (.reconnecting, .reconnecting):
                return true
            case (.error(let lhsMessage), .error(let rhsMessage)):
                return lhsMessage == rhsMessage
            default:
                return false
            }
        }
    }
    
    public enum ClientError: Error, LocalizedError {
        case invalidURL
        case connectionFailed
        case notConnected
        case encodingFailed
        case decodingFailed
        
        public var errorDescription: String? {
            switch self {
            case .invalidURL: return "Invalid WebSocket URL"
            case .connectionFailed: return "WebSocket connection failed"
            case .notConnected: return "WebSocket not connected"
            case .encodingFailed: return "Failed to encode message"
            case .decodingFailed: return "Failed to decode message"
            }
        }
    }
    
    // MARK: - Properties
    
    private let serverURL: URL
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private var isManualDisconnect = false
    private var reconnectTask: Task<Void, Never>?
    private var pingTask: Task<Void, Never>?
    
    // Configuration
    public let reconnectInterval: TimeInterval = 5.0
    public let pingInterval: TimeInterval = 30.0
    public let maxReconnectAttempts: Int = 10
    private var reconnectAttempts = 0
    
    // State
    private var _state: State = .disconnected
    public var state: State { _state }
    
    // Delegate using AsyncStream
    private var stateStream: AsyncStream<State>.Continuation?
    private var messageStream: AsyncStream<Data>.Continuation?
    private var errorStream: AsyncStream<Error>.Continuation?
    
    // MARK: - Initialization
    
    public init(serverURL: URL) {
        self.serverURL = serverURL
        Task { await setupURLSession() }
    }
    
    deinit {
        // 在 deinit 中不能使用 async，只能同步断开连接
        webSocketTask?.cancel(with: .goingAway, reason: nil)
    }
    
    // MARK: - Public Interface
    
    public func connect() async throws {
        guard _state == .disconnected || _state == .reconnecting else {
            print("WSClient: Already connected or connecting")
            return
        }
        
        isManualDisconnect = false
        setState(.connecting)
        
        guard let urlSession = urlSession else {
            throw ClientError.connectionFailed
        }
        
        // Create WebSocket connection
        var request = URLRequest(url: serverURL)
        request.setValue("websocket", forHTTPHeaderField: "Upgrade")
        request.setValue("Upgrade", forHTTPHeaderField: "Connection")
        request.setValue("13", forHTTPHeaderField: "Sec-WebSocket-Version")
        request.setValue(generateWebSocketKey(), forHTTPHeaderField: "Sec-WebSocket-Key")
        
        webSocketTask = urlSession.webSocketTask(with: request)
        webSocketTask?.resume()
        
        // Start receiving messages
        startReceiving()
        
        print("WSClient: Connecting to \(serverURL)")
    }
    
    public func disconnect() async {
        isManualDisconnect = true
        stopReconnectTask()
        stopPingTask()
        
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        
        setState(.disconnected)
        print("WSClient: Disconnected")
    }
    
    public func sendProtobufData(_ data: Data) async throws {
        guard _state == .connected else {
            throw ClientError.notConnected
        }
        
        guard let webSocketTask = webSocketTask else {
            throw ClientError.notConnected
        }
        
        let message = URLSessionWebSocketTask.Message.data(data)
        try await webSocketTask.send(message)
    }
    
    public func sendPing() async throws {
        guard let webSocketTask = webSocketTask else {
            throw ClientError.notConnected
        }
        
        webSocketTask.sendPing { error in
            if let error = error {
                print("WSClient: Ping failed with error: \(error)")
            }
        }
    }
    
    // MARK: - Streams for Communication
    
    public var stateUpdates: AsyncStream<State> {
        AsyncStream { continuation in
            self.stateStream = continuation
        }
    }
    
    public var messages: AsyncStream<Data> {
        AsyncStream { continuation in
            self.messageStream = continuation
        }
    }
    
    public var errors: AsyncStream<Error> {
        AsyncStream { continuation in
            self.errorStream = continuation
        }
    }
    
    // MARK: - Private Methods
    
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        urlSession = URLSession(configuration: config, delegate: nil, delegateQueue: nil)
    }
    
    private func setState(_ newState: State) {
        _state = newState
        stateStream?.yield(newState)
    }
    
    private func startReceiving() {
        Task {
            while !isManualDisconnect {
                do {
                    guard let webSocketTask = webSocketTask else { break }
                    let message = try await webSocketTask.receive()
                    await handleReceivedMessage(message)
                } catch {
                    print("WSClient: Receive error: \(error)")
                    await handleConnectionError(error)
                    break
                }
            }
        }
    }
    
    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            messageStream?.yield(data)
        case .string(let text):
            if let data = text.data(using: .utf8) {
                messageStream?.yield(data)
            }
        @unknown default:
            print("WSClient: Received unknown message type")
        }
    }
    
    private func handleConnectionError(_ error: Error) async {
        setState(.error(error.localizedDescription))
        errorStream?.yield(error)
        
        if !isManualDisconnect {
            await scheduleReconnect()
        }
    }
    
    private func scheduleReconnect() async {
        guard reconnectAttempts < maxReconnectAttempts else {
            print("WSClient: Max reconnect attempts reached")
            setState(.disconnected)
            return
        }
        
        reconnectAttempts += 1
        setState(.reconnecting)
        
        print("WSClient: Scheduling reconnect attempt \(reconnectAttempts)/\(maxReconnectAttempts)")
        
        reconnectTask = Task {
            try? await Task.sleep(nanoseconds: UInt64(reconnectInterval * 1_000_000_000))
            if !Task.isCancelled {
                try? await connect()
            }
        }
    }
    
    private func stopReconnectTask() {
        reconnectTask?.cancel()
        reconnectTask = nil
    }
    
    private func startPingTask() {
        stopPingTask()
        pingTask = Task {
            while !Task.isCancelled && _state == .connected {
                try? await Task.sleep(nanoseconds: UInt64(pingInterval * 1_000_000_000))
                if !Task.isCancelled {
                    try? await sendPing()
                }
            }
        }
    }
    
    private func stopPingTask() {
        pingTask?.cancel()
        pingTask = nil
    }
    
    private func generateWebSocketKey() -> String {
        let keyData = Data((0..<16).map { _ in UInt8.random(in: 0...255) })
        return keyData.base64EncodedString()
    }
    
    // MARK: - Connection Events
    
    public func onConnected() async {
        print("WSClient: WebSocket connected")
        reconnectAttempts = 0
        setState(.connected)
        startPingTask()
    }
    
    public func onDisconnected() async {
        print("WSClient: WebSocket disconnected")
        stopPingTask()
        
        if !isManualDisconnect {
            await scheduleReconnect()
        } else {
            setState(.disconnected)
        }
    }
}
