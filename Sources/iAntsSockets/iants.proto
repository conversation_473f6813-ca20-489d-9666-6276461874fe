syntax = "proto3";

package iAnts;

// 生成 swift ： protoc --swift_out=. iants.proto
// 生成 OC ： protoc --objc_out=. iants.proto
// 生成 C++ ： protoc --cpp_out=. iants.proto
// 生成 OCPP ： protoc --cpp_out=. iants.proto

// -----------------------------------------------------------------------------
// 1. 定义枚举：MessageType

// -----------------------------------------------------------------------------
enum MessageType {
  // 0-9：通用消息
  HEARTBEAT      = 0;   // 心跳消息，包含版本号

  // 10-99：iAntsCore 指令 （iAntsCore收）
  IANTS_VERSION = 10;  // 获取 iAntsCore 版本号
  IANTS_HTTP_START = 11;  // 启动 HTTP 服务
  IANTS_HTTP_SHUTDOWN = 12;  // 关闭 HTTP 服务
  IANTS_HTTP_RESTART = 13;  // 重启 HTTP 服务

  // 100-199：SpringBoard 指令 （SpringBoard收）
  SB_SHOW_ALERT     = 100;  // 弹窗事件
  SB_SHOW_TOAST     = 101;  // 弹吐司事件
  SB_TOUCH_EVENT    = 102;  // 触摸事件
  SB_SCREEN_INFO    = 103;  // 屏幕信息
  SB_FOREGROUND_APP = 104;  // 前台应用
  SB_NOTIFICATION   = 105;  // 系统通知
  SB_SCREENSHOT     = 106;  // 截图
  SB_LOCK_SCREEN    = 107;  // 锁定屏幕
  SB_UNLOCK_SCREEN  = 108;  // 解锁屏幕
  SB_ADVANCED_ALERT_BOX    = 109;  // 高级弹窗

  // 200-299：iAntsRTC 指令 （iAntsRTC收）
  RTC_STATUS  = 200;  // 状态消息
  RTC_DATAANT_ACTION = 201;  // DATAANT指令 dataAnt远控指令
  RTC_IANTS_ACTION = 202;  // IANTS指令 自用的指令
  RTC_SCREENSHOT = 203;  // 截屏
  RTC_SCREEN_CAPTURE = 204;  // 录屏
  RTC_RECORD_AUDIO = 205;  // 录音

  // VNC 方案放弃，因为太慢了

  // 300-399：iAntsUT 指令
  UT_STATUS = 300;  // 状态消息
  UT_GET_CONFIG = 301;  // 获取配置
  UT_ROUTER_CONFIG = 302;  // 路由配置: 转发策略、路由级白名单/黑名单
  UT_ROUTER_ACTION = 303;  // 路由动作：添加、删除、修改、恢复默认
  UT_IP_CONFIG = 304;  // 配置utun的IP
  UT_PROXY_CONFIG = 305;  // 配置代理: 代理类型、代理地址、代理端口、代理用户名、代理密码
  UT_PROXY_ACTION = 306;  // 代理动作：启动、停止、重启
  UT_PROXY_STATUS = 307;  // 代理状态: 代理是否启动

  UT_DEAFULT = 399;  //  恢复默认配置（所有配置都恢复到默认值）

  ERROR          = 999;  // 错误消息（通用）
}

// -----------------------------------------------------------------------------
// 2. 定义各个具体消息体
//    对应原来每种 IAMessageType 的“data”部份。
// -----------------------------------------------------------------------------

// 2.1 心跳消息，原来是一个 string（版本号）
message Heartbeat {
  string version = 1;  // 发送者的版本号
}

// 2.2 弹窗消息：标题、内容和消失时间
message SB_Alert {
  string title = 1;        // 标题
  string content = 2;      // 弹窗内容
  int32 dismissTime = 3;   // 消失时间（秒）
}

// 2.3 弹吐司消息：包含完整参数
message SB_Toast {
  string content = 1;      // 吐司内容
  int32 type = 2;          // 吐司类型
  float duration = 3;      // 持续时间
  int32 position = 4;      // 位置：0=top, 1=bottom, 2=left, 3=right
  int32 fontSize = 5;      // 字体大小
}

// 2.4 触摸事件：坐标 + 动作类型
message SB_TouchEvent {
  int32 x      = 1;
  int32 y      = 2;
  string action = 3;  // "tap", "swipe" 等
}

// 2.5 请求屏幕信息（无附加字段，通知端只要收到就发信息）
message SB_ScreenInfo {
  // 本条可以留空，也可以未来扩展字段
}

// 2.6 前台应用（无附加字段）
message SB_ForegroundApp {
}

// 2.7 发送系统通知
message SB_Notification {
  string title = 1;
  string body  = 2;
}

// 2.8 请求截图（无附加字段）
message SB_Screenshot {
}

// 2.9 锁屏（无附加字段）
message SB_LockScreen {
}

// 2.10 解锁（无附加字段）
message SB_UnlockScreen {
}

// 2.11 高级弹窗消息：包含完整的弹窗参数
message SB_AdvancedAlert {
  string title = 1;                      // 可选：弹窗标题
  string content = 2;                    // 可选：弹窗内容
  string defaultButtonTitle = 3;         // 可选：主按钮标题
  string alternateButtonTitle = 4;       // 可选：备用按钮标题
  string otherButtonTitle = 5;           // 可选：第三按钮标题
  repeated string checkBoxTitles = 6;    // 可选：复选框标题数组
  repeated string textFieldTitles = 7;   // 可选：输入框标题数组
  repeated string textFieldValues = 8;   // 可选：输入框初始值数组
  repeated string popUpTitles = 9;       // 可选：下拉菜单选项数组
  int32 popUpSelection = 10;             // 可选：下拉菜单默认选中索引
  float progressValue = 11;              // 可选：进度条数值（0.0~1.0）
  string iconURL = 12;                   // 可选：自定义图标URL
  int32 dismissTime = 13;                // 可选：超时时间（秒）
}

// 2.12 错误消息：包含一个错误文本
message ErrorMessage {
  string text = 1;
}

// 2.13 UT 状态消息
message UT_StatusMessage {
  bool utun_created = 1;           // utun 是否已创建
  string interface_name = 2;       // 接口名称 (如 utun5)
  int32 unit = 3;                  // 单元号
  string ip_address = 4;           // IP 地址
  string dest_address = 5;         // 目标地址
  int32 mtu = 6;                   // MTU 大小
  bool interface_up = 7;           // 接口是否启用
  bool proxy_running = 8;          // 代理是否运行中
  string proxy_url = 9;            // 代理 URL
  repeated string routes = 10;     // 路由列表
}

// 2.14 UT 获取配置
message UT_GetConfigMessage {
  // 空消息，用于请求当前配置
}

// 2.15 UT 路由配置
message UT_RouterConfigMessage {
  string strategy = 1;             // 转发策略: "direct", "proxy", "auto"
  repeated string whitelist = 2;   // 白名单 IP/域名
  repeated string blacklist = 3;   // 黑名单 IP/域名
  bool enable_dns_hijack = 4;      // 是否启用 DNS 劫持
  string dns_server = 5;           // DNS 服务器地址
}

// 2.16 UT 路由动作
message UT_RouterActionMessage {
  enum Action {
    ADD = 0;                       // 添加路由
    DELETE = 1;                    // 删除路由
    MODIFY = 2;                    // 修改路由
    RESTORE_DEFAULT = 3;           // 恢复默认路由
  }
  Action action = 1;
  string destination = 2;          // 目标地址 (如 "default", "***********/24")
  string gateway = 3;              // 网关地址
  string netmask = 4;              // 网络掩码
  string interface = 5;            // 接口名称
  int32 metric = 6;                // 路由度量值
}

// 2.17 UT IP 配置
message UT_IPConfigMessage {
  string ip_address = 1;           // IP 地址
  string dest_address = 2;         // 目标地址 (点对点)
  string netmask = 3;              // 网络掩码
  int32 mtu = 4;                   // MTU 大小
  bool bring_up = 5;               // 是否启用接口
}

// 2.18 UT 代理配置
message UT_ProxyConfigMessage {
  enum ProxyType {
    HTTP = 0;
    HTTPS = 1;
    SOCKS5 = 2;
    SHADOWSOCKS = 3;
  }
  ProxyType type = 1;
  string host = 2;                 // 代理主机
  int32 port = 3;                  // 代理端口
  string username = 4;             // 用户名 (可选)
  string password = 5;             // 密码 (可选)
  string extra_config = 6;         // 额外配置 (JSON 格式)
}

// 2.19 UT 代理动作
message UT_ProxyActionMessage {
  enum Action {
    START = 0;                     // 启动代理
    STOP = 1;                      // 停止代理
    RESTART = 2;                   // 重启代理
  }
  Action action = 1;
}

// 2.20 UT 代理状态
message UT_ProxyStatusMessage {
  bool running = 1;                // 是否运行中
  string proxy_url = 2;            // 当前代理 URL
  int64 bytes_sent = 3;            // 发送字节数
  int64 bytes_received = 4;        // 接收字节数
  int64 start_time = 5;            // 启动时间戳
}

// 2.21 UT 恢复默认配置
message UT_DefaultMessage {
  // 空消息，用于恢复所有默认配置
}

// -----------------------------------------------------------------------------
// 3. 最外层包装消息：IAntsMessage，使用 oneof 来保证“type”与“payload”对齐
//    “type”字段决定了具体 payload 使用哪一个子消息。
// -----------------------------------------------------------------------------
message Message {
  MessageType type = 1;

  oneof body {
    Heartbeat   heartbeat      = 2;
    SB_Alert       showAlert      = 3;
    SB_Toast       showToast      = 4;
    SB_TouchEvent  touchEvent     = 5;
    SB_ScreenInfo  screenInfo     = 6;
    SB_ForegroundApp foregroundApp = 7;
    SB_Notification notification   = 8;
    SB_Screenshot  screenshot      = 9;
    SB_LockScreen  lockScreen      = 10;
    SB_UnlockScreen unlockScreen   = 11;
    SB_AdvancedAlert  advancedAlert  = 12;
    ErrorMessage       error           = 13;

    // UT 消息 (300-399)
    UT_StatusMessage      ut_status        = 14;
    UT_GetConfigMessage   ut_get_config    = 15;
    UT_RouterConfigMessage ut_router_config = 16;
    UT_RouterActionMessage ut_router_action = 17;
    UT_IPConfigMessage    ut_ip_config     = 18;
    UT_ProxyConfigMessage ut_proxy_config  = 19;
    UT_ProxyActionMessage ut_proxy_action  = 20;
    UT_ProxyStatusMessage ut_proxy_status  = 21;
    UT_DefaultMessage     ut_default       = 22;
  }
}