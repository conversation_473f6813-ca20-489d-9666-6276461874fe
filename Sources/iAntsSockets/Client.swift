/// 启动 Websocket 客户端，监听 WS 消息，WS需要长时间保活
/// 


// 收到消息：protobuf 解码 -> handle 




// RTC_DATAANT_ACTION: 创建一个 RtcServices 实例 （超时自动销毁机制）



/// ScreenCapture流程
/// 1. 检查ScreenCapture是否启动没启动则启动 ：TODO ScreenCapture 需要添加引用计数，如果==0则自动关闭，每增加一个WEBRTC 实例 则+1，销毁-1 或者 使用多播委托（最优）吧
/// 2. 没启动则启动ScreenCapture，确保ScreenCapture只启动一次，如果没有引用或者委托则自动关停
/// 
/// 
/// RtcServices 流程
/// 启动一个RtcServices实例，两种类别
///  1. RtcServices：启动信令交换监听消息
/// 2. 收到远程启动信号
/// 3. 交换 ICE
/// 4. 建立连接
/// 5. 根据不同的模式：开始传输视频
/// 同时需要实现. 信令服务保活 webRTC断开自动销毁实例
/// 
/// 
/// 截图流程：ScreenCapture直接封装出一个接口
/// 录屏流程：和RtcServices差不多，使用代理委托模式，同时录音为可选参数
/// 录音 这个单独交互