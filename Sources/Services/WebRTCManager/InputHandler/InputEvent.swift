import Foundation
import CoreGraphics
import UIKit

/// 输入事件类型
enum InputEventType: String {
    case input = "INPUT"   // 键盘输入
    case scroll = "SCROLL" // 滚动
    case down = "DOWN"     // 触摸按下
    case up = "UP"         // 触摸抬起
    case move = "MOVE"     // 触摸移动
    case shell = "SHELL"   // Shell 命令（适配 Android 操作）
}

/// 输入事件基类
protocol InputEvent {
    var type: InputEventType { get }
}

/// 键盘输入事件
struct KeyboardInputEvent: InputEvent {
    let type: InputEventType = .input
    let text: String
}

/// 滚动事件
struct ScrollEvent: InputEvent {
    let type: InputEventType = .scroll
    let deltaH: CGFloat
}

/// 触摸事件
struct TouchEvent: InputEvent {
    let type: InputEventType
    let positionX: CGFloat
    let positionY: CGFloat
}

/// Shell 命令事件（适配 Android 操作）
struct ShellEvent: InputEvent {
    let type: InputEventType = .shell
    let cmd: String
}

/// 输入事件工厂
class InputEventFactory {
    static func createEvent(from data: Data) -> InputEvent? {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let typeString = json["type"] as? String,
              let eventType = InputEventType(rawValue: typeString) else {
            return nil
        }

        switch eventType {
        case .input:
            guard let word = json["word"] as? String else { return nil }
            return KeyboardInputEvent(text: word)

        case .scroll:
            guard let deltaH = json["h"] as? CGFloat else { return nil }
            return ScrollEvent(deltaH: deltaH)

        case .down, .up, .move:
            guard let posX = json["x"] as? CGFloat,
                  let posY = json["y"] as? CGFloat else { return nil }
            return TouchEvent(type: eventType, positionX: posX, positionY: posY)

        case .shell:
            guard let cmd = json["cmd"] as? String else { return nil }
            return ShellEvent(cmd: cmd)
        }
    }
}

// MARK: - 简化的输入处理（委托给 OC 实现）

// IOKit 相关代码已移至 OC 实现

/// 系统级输入事件处理器（单例）- 简化版，委托给 OC 实现
class SystemInputHandler {
    static let shared = SystemInputHandler()

    private let logger = DaLog(subsystem: "com.iantsrtc.input", category: "SystemInputHandler")
    private let screenSize: CGSize

    // 触摸状态跟踪
    private var isCurrentlyTouching = false
    private var lastTouchX: CGFloat = 0
    private var lastTouchY: CGFloat = 0

    private init() {
        // 获取实际屏幕尺寸（像素级别）
        let screenBounds = UIScreen.main.bounds
        let screenScale = UIScreen.main.scale
        self.screenSize = CGSize(
            width: screenBounds.width * screenScale,
            height: screenBounds.height * screenScale
        )

        // 设置屏幕尺寸到 OC 实现
        InputEventHandler.setScreenWidth(size_t(screenSize.width), height: size_t(screenSize.height))

        logger.info("🎮 系统输入处理器初始化完成")
        logger.info("   实际屏幕尺寸: \(Int(screenSize.width))x\(Int(screenSize.height)) 像素")
        logger.info("   坐标系统: 左上角(0,0) -> 右下角(\(screenSize.width),\(screenSize.height))")
        logger.info("   使用 OC 实现的 IOKit 处理")
    }

    /// 处理输入事件（主要入口方法）
    func handleInputEvent(jsonData: Data) {
        guard let inputEvent = InputEventFactory.createEvent(from: jsonData) else {
            logger.warning("⚠️ 无法解析输入事件")
            return
        }

        switch inputEvent.type {
        case .input:
            if let keyboardEvent = inputEvent as? KeyboardInputEvent {
                handleKeyboardInput(keyboardEvent)
            }
        case .scroll:
            if let scrollEvent = inputEvent as? ScrollEvent {
                handleScrollInput(scrollEvent)
            }
        case .down, .up, .move:
            if let touchEvent = inputEvent as? TouchEvent {
                handleTouchInput(touchEvent)
            }
        case .shell:
            if let shellEvent = inputEvent as? ShellEvent {
                handleShellInput(shellEvent)
            }
        }
    }

    /// 处理键盘输入 - 委托给 OC 实现
    private func handleKeyboardInput(_ event: KeyboardInputEvent) {
        logger.debug("📝 键盘输入: \(event.text)")

        // 过滤掉单独的 Meta 输入
        if event.text.trimmingCharacters(in: .whitespacesAndNewlines) == "Meta" {
            logger.info("⏭️ 跳过单独的 Meta 输入")
            return
        }

        // 委托给 OC 实现
        InputEventHandler.handleKeyboardInput(withText: event.text)

        logger.info("✅ 键盘输入处理完成: \(event.text)")
    }

    // 字符映射和键盘事件发送已移至 OC 实现

    /// 处理滚动事件 - 委托给 OC 实现
    private func handleScrollInput(_ event: ScrollEvent) {
        logger.debug("📜 滚动: \(event.deltaH)")

        // 委托给 OC 实现
        InputEventHandler.handleScroll(withDeltaH: event.deltaH)

        logger.info("✅ 滚动事件处理完成: \(event.deltaH)")
    }

    /// 处理 Shell 命令事件 - 委托给 OC 实现（适配 Android 操作）
    private func handleShellInput(_ event: ShellEvent) {
        logger.debug("🐚 Shell 命令: \(event.cmd)")

        // 委托给 OC 实现
        InputEventHandler.handleShellCommand(event.cmd)

        logger.info("✅ Shell 命令处理完成: \(event.cmd)")
    }

    /// 处理触摸事件 - 委托给 OC 实现
    private func handleTouchInput(_ event: TouchEvent) {
        // 传入的坐标已经是归一化坐标(0.0-1.0)，直接使用！
        let normalizedX = event.positionX
        let normalizedY = event.positionY

        // 计算像素坐标用于日志显示
        let pixelX = normalizedX * screenSize.width
        let pixelY = normalizedY * screenSize.height

        logger.debug("👆 触摸\(event.type.rawValue): 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY))) -> 像素(\(Int(pixelX)), \(Int(pixelY)))")

        switch event.type {
        case .down:
            logger.info("🔽 触摸按下: 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY)))")
            InputEventHandler.handleTouchDownWith(x: normalizedX, y: normalizedY)
            isCurrentlyTouching = true
            lastTouchX = normalizedX
            lastTouchY = normalizedY

        case .up:
            logger.info("🔼 触摸抬起: 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY)))")
            InputEventHandler.handleTouchUpWith(x: normalizedX, y: normalizedY)
            isCurrentlyTouching = false

        case .move:
            if isCurrentlyTouching {
                logger.debug("↔️ 触摸移动: 归一化(\(String(format: "%.3f", normalizedX)), \(String(format: "%.3f", normalizedY)))")
                InputEventHandler.handleTouchMoveWith(x: normalizedX, y: normalizedY)
                lastTouchX = normalizedX
                lastTouchY = normalizedY
            } else {
                logger.warning("⚠️ 收到移动事件但当前未在触摸状态")
            }

        default:
            break
        }

        logger.info("✅ 触摸事件处理完成: \(event.type.rawValue)")
    }

    // 所有 IOKit 相关的触摸事件处理已移至 OC 实现
}
