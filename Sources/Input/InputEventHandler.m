//
//  InputEventHandler.m
//  iAntsRTC
//
//  Created by YC on 2025/01/21.
//

#import "InputEventHandler.h"
#import <mach/mach.h>
#import <mach/mach_time.h>
#import <CoreGraphics/CoreGraphics.h>

// 如果工程中已有 IOKit 相关头文件则可使用现成的，这里假设路径如下：
#import "IOKit/hid/IOHIDEventTypes.h"
#import "IOKit/hidsystem/IOHIDUsageTables.h"

#pragma mark - 类型定义

typedef uint32_t IOHIDDigitizerTransducerType;
#ifdef __LP64__
typedef double IOHIDFloat;
#else
typedef float IOHIDFloat;
#endif

typedef UInt32 IOOptionBits;
typedef uint32_t IOHIDEventField;

// 此处声明 HID 事件相关接口（若系统已有对应声明，可省略）
typedef uint32_t IOHIDEventOptionBits;
typedef struct __IOHIDEvent *IOHIDEventRef;
typedef struct __IOHIDEventSystemClient *IOHIDEventSystemClientRef;

// IOKit 函数声明
IOHIDEventRef IOHIDEventCreateKeyboardEvent(CFAllocatorRef allocator,
                                             uint64_t time,
                                             uint16_t page,
                                             uint16_t usage,
                                             Boolean down,
                                             IOHIDEventOptionBits flags);

IOHIDEventRef IOHIDEventCreateDigitizerEvent(CFAllocatorRef allocator,
                                              uint64_t timeStamp,
                                              IOHIDDigitizerTransducerType type,
                                              uint32_t index,
                                              uint32_t identity,
                                              uint32_t eventMask,
                                              uint32_t buttonMask,
                                              IOHIDFloat x,
                                              IOHIDFloat y,
                                              IOHIDFloat z,
                                              IOHIDFloat tipPressure,
                                              IOHIDFloat barrelPressure,
                                              Boolean range,
                                              Boolean touch,
                                              IOOptionBits options);

IOHIDEventRef IOHIDEventCreateDigitizerFingerEvent(CFAllocatorRef allocator,
                                                    uint64_t timeStamp,
                                                    uint32_t index,
                                                    uint32_t identity,
                                                    uint32_t eventMask,
                                                    IOHIDFloat x,
                                                    IOHIDFloat y,
                                                    IOHIDFloat z,
                                                    IOHIDFloat tipPressure,
                                                    IOHIDFloat twist,
                                                    Boolean range,
                                                    Boolean touch,
                                                    IOOptionBits options);

IOHIDEventSystemClientRef IOHIDEventSystemClientCreate(CFAllocatorRef allocator);

void IOHIDEventAppendEvent(IOHIDEventRef parent, IOHIDEventRef child);
void IOHIDEventSetIntegerValue(IOHIDEventRef event, IOHIDEventField field, int value);
void IOHIDEventSetSenderID(IOHIDEventRef event, uint64_t sender);

void IOHIDEventSystemClientDispatchEvent(IOHIDEventSystemClientRef client, IOHIDEventRef event);

// 添加缺少的 HID Usage 常量
#define kHIDPage_Consumer                   0x0C
#define kHIDPage_KeyboardOrKeypad          0x07
#define kHIDUsage_Csmr_Power               0x30
#define kHIDUsage_Csmr_Menu                0x40
#define kHIDUsage_Csmr_VolumeIncrement     0xE9
#define kHIDUsage_Csmr_VolumeDecrement     0xEA
#define kHIDUsage_Csmr_ACBack              0x0224
#define kHIDUsage_KeyboardEscape           0x29
#define kHIDUsage_KeyboardLeftGUI          0xE3
#define kHIDUsage_KeyboardLeftArrow        0x50

#pragma mark - 内部静态变量

// 用于指针坐标归一化的屏幕尺寸（默认值 0，外部可通过 setScreenWidth:height: 设置）
static size_t sScreenWidth = 0;
static size_t sScreenHeight = 0;

// 记录指针当前状态
static bool sIsCurrentlyTouching = false;

#pragma mark - 内部静态函数

// 供内部调用，用于发送 HID 事件
static void SendHIDEvent(IOHIDEventRef event) {
    static IOHIDEventSystemClientRef client = NULL;
    if (client == NULL) {
        client = IOHIDEventSystemClientCreate(kCFAllocatorDefault);
    }
    // 固定的 senderID（与原始代码一致）
    IOHIDEventSetSenderID(event, 0x8000000817319372);
    IOHIDEventSystemClientDispatchEvent(client, event);
    CFRelease(event);
}

// 处理键盘事件的内部实现
static void HandleKeyboardInput(NSString *text) {
    for (NSUInteger i = 0; i < text.length; i++) {
        unichar character = [text characterAtIndex:i];
        uint16_t usage = 0;
        
        // 字符映射到HID键盘使用码
        switch (character) {
            // 数字键
            case '1': case '!': usage = kHIDUsage_Keyboard1; break;
            case '2': case '@': usage = kHIDUsage_Keyboard2; break;
            case '3': case '#': usage = kHIDUsage_Keyboard3; break;
            case '4': case '$': usage = kHIDUsage_Keyboard4; break;
            case '5': case '%': usage = kHIDUsage_Keyboard5; break;
            case '6': case '^': usage = kHIDUsage_Keyboard6; break;
            case '7': case '&': usage = kHIDUsage_Keyboard7; break;
            case '8': case '*': usage = kHIDUsage_Keyboard8; break;
            case '9': case '(': usage = kHIDUsage_Keyboard9; break;
            case '0': case ')': usage = kHIDUsage_Keyboard0; break;
                
            // 字母键（大小写）
            case 'A': case 'a': usage = kHIDUsage_KeyboardA; break;
            case 'B': case 'b': usage = kHIDUsage_KeyboardB; break;
            case 'C': case 'c': usage = kHIDUsage_KeyboardC; break;
            case 'D': case 'd': usage = kHIDUsage_KeyboardD; break;
            case 'E': case 'e': usage = kHIDUsage_KeyboardE; break;
            case 'F': case 'f': usage = kHIDUsage_KeyboardF; break;
            case 'G': case 'g': usage = kHIDUsage_KeyboardG; break;
            case 'H': case 'h': usage = kHIDUsage_KeyboardH; break;
            case 'I': case 'i': usage = kHIDUsage_KeyboardI; break;
            case 'J': case 'j': usage = kHIDUsage_KeyboardJ; break;
            case 'K': case 'k': usage = kHIDUsage_KeyboardK; break;
            case 'L': case 'l': usage = kHIDUsage_KeyboardL; break;
            case 'M': case 'm': usage = kHIDUsage_KeyboardM; break;
            case 'N': case 'n': usage = kHIDUsage_KeyboardN; break;
            case 'O': case 'o': usage = kHIDUsage_KeyboardO; break;
            case 'P': case 'p': usage = kHIDUsage_KeyboardP; break;
            case 'Q': case 'q': usage = kHIDUsage_KeyboardQ; break;
            case 'R': case 'r': usage = kHIDUsage_KeyboardR; break;
            case 'S': case 's': usage = kHIDUsage_KeyboardS; break;
            case 'T': case 't': usage = kHIDUsage_KeyboardT; break;
            case 'U': case 'u': usage = kHIDUsage_KeyboardU; break;
            case 'V': case 'v': usage = kHIDUsage_KeyboardV; break;
            case 'W': case 'w': usage = kHIDUsage_KeyboardW; break;
            case 'X': case 'x': usage = kHIDUsage_KeyboardX; break;
            case 'Y': case 'y': usage = kHIDUsage_KeyboardY; break;
            case 'Z': case 'z': usage = kHIDUsage_KeyboardZ; break;
                
            // 特殊字符
            case '_': case '-': usage = kHIDUsage_KeyboardHyphen; break;
            case '+': case '=': usage = kHIDUsage_KeyboardEqualSign; break;
            case '{': case '[': usage = kHIDUsage_KeyboardOpenBracket; break;
            case '}': case ']': usage = kHIDUsage_KeyboardCloseBracket; break;
            case '|': case '\\': usage = kHIDUsage_KeyboardBackslash; break;
            case ':': case ';': usage = kHIDUsage_KeyboardSemicolon; break;
            case '"': case '\'': usage = kHIDUsage_KeyboardQuote; break;
            case '~': case '`': usage = kHIDUsage_KeyboardGraveAccentAndTilde; break;
            case '<': case ',': usage = kHIDUsage_KeyboardComma; break;
            case '>': case '.': usage = kHIDUsage_KeyboardPeriod; break;
            case '?': case '/': usage = kHIDUsage_KeyboardSlash; break;
                
            // 控制字符
            case '\n': case '\r': usage = kHIDUsage_KeyboardReturnOrEnter; break;
            case '\t': usage = kHIDUsage_KeyboardTab; break;
            case ' ': usage = kHIDUsage_KeyboardSpacebar; break;
                
            default:
                continue; // 跳过不支持的字符
        }
        
        if (usage != 0) {
            // 发送按下事件
            IOHIDEventRef downEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                     mach_absolute_time(),
                                                                     kHIDPage_KeyboardOrKeypad,
                                                                     usage,
                                                                     true,
                                                                     0);
            SendHIDEvent(downEvent);
            
            // 发送抬起事件
            IOHIDEventRef upEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                   mach_absolute_time(),
                                                                   kHIDPage_KeyboardOrKeypad,
                                                                   usage,
                                                                   false,
                                                                   0);
            SendHIDEvent(upEvent);
        }
    }
}

// 处理触摸事件的内部实现（完全按照原始OC版本）
static void HandleTouchEvent(CGFloat x, CGFloat y, bool isTouch, bool isFirstTouch) {
    bool twas = sIsCurrentlyTouching;  // 之前是否在触摸
    bool tis = isTouch;               // 现在是否在触摸

    // 更新触摸状态
    sIsCurrentlyTouching = isTouch;

    uint32_t handm = 0;
    uint32_t fingerm = 0;

    if (!twas && tis) {
        // 开始触摸 - 与OC版本完全一致
        handm = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch | kIOHIDDigitizerEventIdentity;
        fingerm = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch;
    } else if (twas && tis) {
        // 移动触摸 - 与OC版本完全一致
        handm = kIOHIDDigitizerEventPosition;
        fingerm = kIOHIDDigitizerEventPosition;
    } else if (twas && !tis) {
        // 结束触摸 - 与OC版本完全一致
        handm = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch | kIOHIDDigitizerEventIdentity | kIOHIDDigitizerEventPosition;
        fingerm = kIOHIDDigitizerEventRange | kIOHIDDigitizerEventTouch;
    } else {
        // 无效状态，直接返回
        return;
    }

    // 坐标已经是归一化的，直接使用
    IOHIDFloat xf = (IOHIDFloat)x;
    IOHIDFloat yf = (IOHIDFloat)y;

    // 创建手部事件 - 与OC版本完全一致的参数
    IOHIDEventRef hand = IOHIDEventCreateDigitizerEvent(kCFAllocatorDefault,
                                                        mach_absolute_time(),
                                                        kIOHIDDigitizerTransducerTypeHand,
                                                        1 << 22,
                                                        1,
                                                        handm,
                                                        0,
                                                        xf,
                                                        yf,
                                                        0,
                                                        0,
                                                        0,
                                                        false,
                                                        false,
                                                        0);
    IOHIDEventSetIntegerValue(hand, kIOHIDEventFieldIsBuiltIn, true);
    IOHIDEventSetIntegerValue(hand, kIOHIDEventFieldDigitizerIsDisplayIntegrated, true);

    // 创建手指事件 - 与OC版本完全一致的参数
    IOHIDEventRef finger = IOHIDEventCreateDigitizerFingerEvent(kCFAllocatorDefault,
                                                                mach_absolute_time(),
                                                                3,
                                                                2,
                                                                fingerm,
                                                                xf,
                                                                yf,
                                                                0,
                                                                0,
                                                                0,
                                                                tis,
                                                                tis,
                                                                0);
    IOHIDEventAppendEvent(hand, finger);
    CFRelease(finger);

    SendHIDEvent(hand);
}

// 处理滚动事件的内部实现
static void HandleScrollEvent(CGFloat deltaH) {
    // 根据滚动方向发送相应的按键事件
    // 正值向下滚动，负值向上滚动
    uint16_t usage = 0;
    if (deltaH > 0) {
        // 向下滚动 - 发送向下箭头键
        usage = kHIDUsage_KeyboardDownArrow;
    } else if (deltaH < 0) {
        // 向上滚动 - 发送向上箭头键
        usage = kHIDUsage_KeyboardUpArrow;
    } else {
        return; // 无滚动
    }

    // 发送按下事件
    IOHIDEventRef downEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                             mach_absolute_time(),
                                                             kHIDPage_KeyboardOrKeypad,
                                                             usage,
                                                             true,
                                                             0);
    SendHIDEvent(downEvent);

    // 发送抬起事件
    IOHIDEventRef upEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                           mach_absolute_time(),
                                                           kHIDPage_KeyboardOrKeypad,
                                                           usage,
                                                           false,
                                                           0);
    SendHIDEvent(upEvent);
}

// 处理 Shell 命令的内部实现（适配 Android 操作）
static void HandleShellCommand(NSString *cmd) {
    NSLog(@"🐚 处理 Shell 命令: %@", cmd);

    // 适配 Android 的操作，后续需要简化
    if ([cmd containsString:@"KEYCODE_HOME"]) {
        // HOME 按钮 - 使用 Consumer Control HID usage
        NSLog(@"🏠 执行 HOME 操作");
        IOHIDEventRef event = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                             mach_absolute_time(),
                                                             kHIDPage_Consumer,
                                                             kHIDUsage_Csmr_Menu, // 使用菜单键作为 HOME
                                                             true,
                                                             0);
        SendHIDEvent(event);

        // 发送抬起事件
        IOHIDEventRef upEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                               mach_absolute_time(),
                                                               kHIDPage_Consumer,
                                                               kHIDUsage_Csmr_Menu,
                                                               false,
                                                               0);
        SendHIDEvent(upEvent);

    } else if ([cmd containsString:@"KEYCODE_BACK"]) {
        // BACK 操作 - 尝试多种方式
        NSLog(@"⬅️ 执行 BACK 操作");

        // 方法1: 尝试使用 Consumer Control 的 AC Back
        IOHIDEventRef backEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                 mach_absolute_time(),
                                                                 kHIDPage_Consumer,
                                                                 kHIDUsage_Csmr_ACBack,
                                                                 true,
                                                                 0);
        SendHIDEvent(backEvent);

        IOHIDEventRef backUpEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                   mach_absolute_time(),
                                                                   kHIDPage_Consumer,
                                                                   kHIDUsage_Csmr_ACBack,
                                                                   false,
                                                                   0);
        SendHIDEvent(backUpEvent);

        // 方法2: 备用方案 - 使用 Command+Left Arrow (类似浏览器后退)
        // 如果上面不生效，可以尝试这个
        /*
        IOHIDEventRef cmdEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                               mach_absolute_time(),
                                                               kHIDPage_KeyboardOrKeypad,
                                                               kHIDUsage_KeyboardLeftGUI,
                                                               true,
                                                               0);
        SendHIDEvent(cmdEvent);

        IOHIDEventRef leftEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                mach_absolute_time(),
                                                                kHIDPage_KeyboardOrKeypad,
                                                                kHIDUsage_KeyboardLeftArrow,
                                                                true,
                                                                0);
        SendHIDEvent(leftEvent);

        IOHIDEventRef leftUpEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                   mach_absolute_time(),
                                                                   kHIDPage_KeyboardOrKeypad,
                                                                   kHIDUsage_KeyboardLeftArrow,
                                                                   false,
                                                                   0);
        SendHIDEvent(leftUpEvent);

        IOHIDEventRef cmdUpEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                 mach_absolute_time(),
                                                                 kHIDPage_KeyboardOrKeypad,
                                                                 kHIDUsage_KeyboardLeftGUI,
                                                                 false,
                                                                 0);
        SendHIDEvent(cmdUpEvent);
        */

    } else if ([cmd containsString:@"KEYCODE_MENU"]) {
        // 菜单操作 - iOS 对应打开后台任务栏，使用双击 HOME 的效果
        NSLog(@"📱 执行 MENU 操作（双击 HOME 打开后台任务栏）");

        // 第一次点击
        IOHIDEventRef event1 = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                              mach_absolute_time(),
                                                              kHIDPage_Consumer,
                                                              kHIDUsage_Csmr_Menu,
                                                              true,
                                                              0);
        SendHIDEvent(event1);

        IOHIDEventRef upEvent1 = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                mach_absolute_time(),
                                                                kHIDPage_Consumer,
                                                                kHIDUsage_Csmr_Menu,
                                                                false,
                                                                0);
        SendHIDEvent(upEvent1);

        // 短暂延迟（模拟双击间隔）
        usleep(100000); // 100ms 延迟

        // 第二次点击
        IOHIDEventRef event2 = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                              mach_absolute_time(),
                                                              kHIDPage_Consumer,
                                                              kHIDUsage_Csmr_Menu,
                                                              true,
                                                              0);
        SendHIDEvent(event2);

        IOHIDEventRef upEvent2 = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                                mach_absolute_time(),
                                                                kHIDPage_Consumer,
                                                                kHIDUsage_Csmr_Menu,
                                                                false,
                                                                0);
        SendHIDEvent(upEvent2);

    } else if ([cmd containsString:@"KEYCODE_POWER"]) {
        // 电源按钮 - 使用 Consumer Control HID usage
        NSLog(@"🔌 执行 POWER 操作");
        IOHIDEventRef event = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                             mach_absolute_time(),
                                                             kHIDPage_Consumer,
                                                             kHIDUsage_Csmr_Power,
                                                             true,
                                                             0);
        SendHIDEvent(event);

        IOHIDEventRef upEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                               mach_absolute_time(),
                                                               kHIDPage_Consumer,
                                                               kHIDUsage_Csmr_Power,
                                                               false,
                                                               0);
        SendHIDEvent(upEvent);

    } else if ([cmd containsString:@"KEYCODE_VOLUME_UP"]) {
        // 音量增加 - 使用 Consumer Control HID usage
        NSLog(@"🔊 执行 VOLUME_UP 操作");
        IOHIDEventRef event = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                             mach_absolute_time(),
                                                             kHIDPage_Consumer,
                                                             kHIDUsage_Csmr_VolumeIncrement,
                                                             true,
                                                             0);
        SendHIDEvent(event);

        IOHIDEventRef upEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                               mach_absolute_time(),
                                                               kHIDPage_Consumer,
                                                               kHIDUsage_Csmr_VolumeIncrement,
                                                               false,
                                                               0);
        SendHIDEvent(upEvent);

    } else if ([cmd containsString:@"KEYCODE_VOLUME_DOWN"]) {
        // 音量减少 - 使用 Consumer Control HID usage
        NSLog(@"🔉 执行 VOLUME_DOWN 操作");
        IOHIDEventRef event = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                             mach_absolute_time(),
                                                             kHIDPage_Consumer,
                                                             kHIDUsage_Csmr_VolumeDecrement,
                                                             true,
                                                             0);
        SendHIDEvent(event);

        IOHIDEventRef upEvent = IOHIDEventCreateKeyboardEvent(kCFAllocatorDefault,
                                                               mach_absolute_time(),
                                                               kHIDPage_Consumer,
                                                               kHIDUsage_Csmr_VolumeDecrement,
                                                               false,
                                                               0);
        SendHIDEvent(upEvent);

    } else if ([cmd containsString:@"accelerometer_rotation"]) {
        // 屏幕旋转 - 这个比较复杂，可能需要调用私有 API 或系统服务
        NSLog(@"🔄 执行屏幕旋转操作");
        // TODO: 实现屏幕旋转逻辑
        // 可以考虑使用 SpringBoard 的私有 API 或发送特定的系统事件
        NSLog(@"⚠️ 屏幕旋转功能暂未实现，需要调用私有 API");

    } else {
        NSLog(@"❓ 未知的 Shell 命令: %@", cmd);
    }
}

#pragma mark - InputEventHandler 类实现

@implementation InputEventHandler

+ (void)setScreenWidth:(size_t)width height:(size_t)height {
    sScreenWidth = width;
    sScreenHeight = height;
}

+ (void)handleKeyboardInputWithText:(NSString *)text {
    HandleKeyboardInput(text);
}

+ (void)handleTouchDownWithX:(CGFloat)x y:(CGFloat)y {
    HandleTouchEvent(x, y, true, true);
}

+ (void)handleTouchMoveWithX:(CGFloat)x y:(CGFloat)y {
    HandleTouchEvent(x, y, true, false);
}

+ (void)handleTouchUpWithX:(CGFloat)x y:(CGFloat)y {
    HandleTouchEvent(x, y, false, false);
}

+ (void)handleScrollWithDeltaH:(CGFloat)deltaH {
    HandleScrollEvent(deltaH);
}

+ (void)handleShellCommand:(NSString *)cmd {
    HandleShellCommand(cmd);
}

@end
