INSTALL_TARGET_PROCESSES = iAntsRTC
FINALPACKAGE = 1
FRAMEWORK_PATH = $(THEOS_PROJECT_DIR)/layout/Library/Frameworks
FRAMEWORK_PATH2 = $(THEOS_PROJECT_DIR)/frameworks

INCLUDE_PATH = $(THEOS_PROJECT_DIR)/include

UPLOAD ?= 0
##debug
DEBUG ?= 0

BUILD_SCHEME ?= roothide
# THEOS_PACKAGE_SCHEME = rootless
THEOS_PACKAGE_SCHEME=${BUILD_SCHEME}

ifeq ($(THEOS_PACKAGE_SCHEME), rootless)
    # ARCHS :=  arm64 arm64e
	ARCHS :=  arm64
    TARGET := iphone:clang:latest:15.0 ##最低可能到14
	ROOT_SCHEME += -DROOTLESS
	PROG_PATH := /var/jb/usr/sbin/iAntsRTC
else ifeq ($(THEOS_PACKAGE_SCHEME), roothide)
    ARCHS :=  arm64
    TARGET := iphone:clang:latest:15.0 ##最低可能到14
	ROOT_SCHEME += -DROOTHIDE
	PROG_PATH := /usr/sbin/iAntsRTC
else
    # ARCHS := arm64 arm64e
	ARCHS :=  arm64
    TARGET := iphone:clang:latest:12.0
	ROOT_SCHEME += -DROOTFULL
	PROG_PATH := /usr/sbin/iAntsRTC
endif

BUILD_DATE = $(shell date +"%s")

THEOS_DEVICE_IP=localhost
THEOS_DEVICE_PORT=2222

include $(THEOS)/makefiles/common.mk

TOOL_NAME = iAntsRTC

$(TOOL_NAME)_FILES = $(shell find Sources -name "*.swift") $(shell find Sources -name "*.m")
$(TOOL_NAME)_CODESIGN_FLAGS = -Sentitlements.plist
$(TOOL_NAME)_INSTALL_PATH = /usr/sbin

$(TOOL_NAME)_SWIFTFLAGS = -import-objc-header Sources/Bridging-Header.h
$(TOOL_NAME)_CFLAGS = -fobjc-arc -I$(INCLUDE_PATH) 
$(TOOL_NAME)_LDFLAGS = -framework IOKit -ObjC
$(TOOL_NAME)_LDFLAGS += -F$(FRAMEWORK_PATH) -framework WebRTC
$(TOOL_NAME)_LDFLAGS += -F$(FRAMEWORK_PATH2) -framework SwiftProtobuf
$(TOOL_NAME)_FRAMEWORKS := IOSurface CoreFoundation CoreGraphics QuartzCore CoreImage AudioToolbox AVFAudio
$(TOOL_NAME)_PRIVATE_FRAMEWORKS := IOMobileFramebuffer IOSurface CoreFoundation CoreGraphics AudioToolboxCore
$(TOOL_NAME)_EXTRA_FRAMEWORKS := WebRTC
$(TOOL_NAME)_LDFLAGS += -lresolv
$(TOOL_NAME)_LDFLAGS += -Wl,-rpath,@loader_path/.jbroot/Library/Frameworks
## swift
$(TOOL_NAME)_SWIFTFLAGS += -F$(FRAMEWORK_PATH) -framework WebRTC
$(TOOL_NAME)_SWIFTFLAGS += -F$(FRAMEWORK_PATH2)
$(TOOL_NAME)_SWIFTFLAGS += -Xcc -I$(INCLUDE_PATH)
$(TOOL_NAME)_SWIFTFLAGS += \
    -Xlinker -rpath \
    -Xlinker @loader_path/.jbroot/Library/Frameworks

$(TOOL_NAME)_SWIFTFLAGS += -DTHEOS
$(TOOL_NAME)_SWIFTFLAGS += $(ROOT_SCHEME)

## debug
ifeq ($(DEBUG), 1)
    $(TOOL_NAME)_CFLAGS += -g -O0 -DDEBUG
    $(TOOL_NAME)_LDFLAGS += -g
    $(TOOL_NAME)_SWIFTFLAGS += -DDEBUG
endif

after-stage::
	@echo "👉 Patching rpath on the executable and signing"
	install_name_tool \
	  -add_rpath @loader_path/../Library/Frameworks \
	  $(THEOS_STAGING_DIR)/usr/sbin/$(TOOL_NAME)
	ldid -Sentitlements.plist \
	  $(THEOS_STAGING_DIR)/usr/sbin/$(TOOL_NAME)

include $(THEOS_MAKE_PATH)/tool.mk
