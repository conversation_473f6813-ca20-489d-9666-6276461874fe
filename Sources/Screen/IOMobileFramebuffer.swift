import Foundation
import IOSurface
import Accelerate

/// 定义IOMobileFramebuffer相关的函数
@_silgen_name("IOMobileFramebufferGetMainDisplay")
public func IOMobileFramebufferGetMainDisplay(_ connection: UnsafeMutablePointer<UnsafeMutableRawPointer?>) -> Int32

@_silgen_name("IOMobileFramebufferGetLayerDefaultSurface")
public func IOMobileFramebufferGetLayerDefaultSurface(_ connection: UnsafeMutableRawPointer, _ surface: Int32, _ buffer: UnsafeMutablePointer<IOSurfaceRef?>) -> Int32

@_silgen_name("IOMobileFramebufferCopyLayerDisplayedSurface")
public func IOMobileFramebufferCopyLayerDisplayedSurface(_ connection: UnsafeMutableRawPointer, _ surface: Int32, _ buffer: UnsafeMutablePointer<IOSurfaceRef?>) -> Int32 

// MARK: - IOSurfaceAccelerator 私有 API 声明
// 这些是 iOS 私有 API，需要手动声明

typealias IOSurfaceAcceleratorRef = UnsafeMutableRawPointer

// 私有 API 函数声明
@_silgen_name("IOSurfaceAcceleratorCreate")
func IOSurfaceAcceleratorCreate(
    _ allocator: CFAllocator?,
    _ type: Int32,
    _ accelerator: UnsafeMutablePointer<IOSurfaceAcceleratorRef?>
) -> Int32

@_silgen_name("IOSurfaceAcceleratorTransferSurface")
func IOSurfaceAcceleratorTransferSurface(
    _ accelerator: IOSurfaceAcceleratorRef,
    _ source: IOSurfaceRef,
    _ destination: IOSurfaceRef,
    _ dict: UnsafeMutableRawPointer?,
    _ callback: UnsafeMutableRawPointer?,
    _ refcon: UnsafeMutableRawPointer?,
    _ options: UnsafeMutableRawPointer?
) -> UInt32
