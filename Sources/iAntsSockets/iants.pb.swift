// DO NOT EDIT.
// swift-format-ignore-file
// swiftlint:disable all
//
// Generated by the Swift generator plugin for the protocol buffer compiler.
// Source: iants.proto
//
// For information on using the generated types, please see the documentation:
//   https://github.com/apple/swift-protobuf/

import SwiftProtobuf

// If the compiler emits an error on this type, it is because this file
// was generated by a version of the `protoc` Swift plug-in that is
// incompatible with the version of SwiftProtobuf to which you are linking.
// Please ensure that you are building against the same version of the API
// that was used to generate this file.
fileprivate struct _GeneratedWithProtocGenSwiftVersion: SwiftProtobuf.ProtobufAPIVersionCheck {
  struct _2: SwiftProtobuf.ProtobufAPIVersion_2 {}
  typealias Version = _2
}

/// -----------------------------------------------------------------------------
enum IAnts_MessageType: SwiftProtobuf.Enum, Swift.CaseIterable {
  typealias RawValue = Int

  /// 0-9：通用消息
  case heartbeat // = 0

  /// 10-99：iAntsCore 指令 （iAntsCore收）
  case iantsVersion // = 10

  /// 启动 HTTP 服务
  case iantsHTTPStart // = 11

  /// 关闭 HTTP 服务
  case iantsHTTPShutdown // = 12

  /// 重启 HTTP 服务
  case iantsHTTPRestart // = 13

  /// 100-199：SpringBoard 指令 （SpringBoard收）
  case sbShowAlert // = 100

  /// 弹吐司事件
  case sbShowToast // = 101

  /// 触摸事件
  case sbTouchEvent // = 102

  /// 屏幕信息
  case sbScreenInfo // = 103

  /// 前台应用
  case sbForegroundApp // = 104

  /// 系统通知
  case sbNotification // = 105

  /// 截图
  case sbScreenshot // = 106

  /// 锁定屏幕
  case sbLockScreen // = 107

  /// 解锁屏幕
  case sbUnlockScreen // = 108

  /// 高级弹窗
  case sbAdvancedAlertBox // = 109

  /// 200-299：iAntsRTC 指令 （iAntsRTC收）
  case rtcStatus // = 200

  /// DATAANT指令 dataAnt远控指令
  case rtcDataantAction // = 201

  /// IANTS指令 自用的指令
  case rtcIantsAction // = 202

  /// 截屏
  case rtcScreenshot // = 203

  /// 录屏
  case rtcScreenCapture // = 204

  /// 录音
  case rtcRecordAudio // = 205

  /// 300-399：iAntsUT 指令
  case utStatus // = 300

  /// 获取配置
  case utGetConfig // = 301

  /// 路由配置: 转发策略、路由级白名单/黑名单
  case utRouterConfig // = 302

  /// 路由动作：添加、删除、修改、恢复默认
  case utRouterAction // = 303

  /// 配置utun的IP
  case utIpConfig // = 304

  /// 配置代理: 代理类型、代理地址、代理端口、代理用户名、代理密码
  case utProxyConfig // = 305

  /// 代理动作：启动、停止、重启
  case utProxyAction // = 306

  /// 代理状态: 代理是否启动
  case utProxyStatus // = 307

  ///  恢复默认配置（所有配置都恢复到默认值）
  case utDeafult // = 399

  /// 错误消息（通用）
  case error // = 999
  case UNRECOGNIZED(Int)

  init() {
    self = .heartbeat
  }

  init?(rawValue: Int) {
    switch rawValue {
    case 0: self = .heartbeat
    case 10: self = .iantsVersion
    case 11: self = .iantsHTTPStart
    case 12: self = .iantsHTTPShutdown
    case 13: self = .iantsHTTPRestart
    case 100: self = .sbShowAlert
    case 101: self = .sbShowToast
    case 102: self = .sbTouchEvent
    case 103: self = .sbScreenInfo
    case 104: self = .sbForegroundApp
    case 105: self = .sbNotification
    case 106: self = .sbScreenshot
    case 107: self = .sbLockScreen
    case 108: self = .sbUnlockScreen
    case 109: self = .sbAdvancedAlertBox
    case 200: self = .rtcStatus
    case 201: self = .rtcDataantAction
    case 202: self = .rtcIantsAction
    case 203: self = .rtcScreenshot
    case 204: self = .rtcScreenCapture
    case 205: self = .rtcRecordAudio
    case 300: self = .utStatus
    case 301: self = .utGetConfig
    case 302: self = .utRouterConfig
    case 303: self = .utRouterAction
    case 304: self = .utIpConfig
    case 305: self = .utProxyConfig
    case 306: self = .utProxyAction
    case 307: self = .utProxyStatus
    case 399: self = .utDeafult
    case 999: self = .error
    default: self = .UNRECOGNIZED(rawValue)
    }
  }

  var rawValue: Int {
    switch self {
    case .heartbeat: return 0
    case .iantsVersion: return 10
    case .iantsHTTPStart: return 11
    case .iantsHTTPShutdown: return 12
    case .iantsHTTPRestart: return 13
    case .sbShowAlert: return 100
    case .sbShowToast: return 101
    case .sbTouchEvent: return 102
    case .sbScreenInfo: return 103
    case .sbForegroundApp: return 104
    case .sbNotification: return 105
    case .sbScreenshot: return 106
    case .sbLockScreen: return 107
    case .sbUnlockScreen: return 108
    case .sbAdvancedAlertBox: return 109
    case .rtcStatus: return 200
    case .rtcDataantAction: return 201
    case .rtcIantsAction: return 202
    case .rtcScreenshot: return 203
    case .rtcScreenCapture: return 204
    case .rtcRecordAudio: return 205
    case .utStatus: return 300
    case .utGetConfig: return 301
    case .utRouterConfig: return 302
    case .utRouterAction: return 303
    case .utIpConfig: return 304
    case .utProxyConfig: return 305
    case .utProxyAction: return 306
    case .utProxyStatus: return 307
    case .utDeafult: return 399
    case .error: return 999
    case .UNRECOGNIZED(let i): return i
    }
  }

  // The compiler won't synthesize support with the UNRECOGNIZED case.
  static let allCases: [IAnts_MessageType] = [
    .heartbeat,
    .iantsVersion,
    .iantsHTTPStart,
    .iantsHTTPShutdown,
    .iantsHTTPRestart,
    .sbShowAlert,
    .sbShowToast,
    .sbTouchEvent,
    .sbScreenInfo,
    .sbForegroundApp,
    .sbNotification,
    .sbScreenshot,
    .sbLockScreen,
    .sbUnlockScreen,
    .sbAdvancedAlertBox,
    .rtcStatus,
    .rtcDataantAction,
    .rtcIantsAction,
    .rtcScreenshot,
    .rtcScreenCapture,
    .rtcRecordAudio,
    .utStatus,
    .utGetConfig,
    .utRouterConfig,
    .utRouterAction,
    .utIpConfig,
    .utProxyConfig,
    .utProxyAction,
    .utProxyStatus,
    .utDeafult,
    .error,
  ]

}

/// 2.1 心跳消息，原来是一个 string（版本号）
struct IAnts_Heartbeat: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 发送者的版本号
  var version: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.2 弹窗消息：标题、内容和消失时间
struct IAnts_SB_Alert: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 标题
  var title: String = String()

  /// 弹窗内容
  var content: String = String()

  /// 消失时间（秒）
  var dismissTime: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.3 弹吐司消息：包含完整参数
struct IAnts_SB_Toast: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 吐司内容
  var content: String = String()

  /// 吐司类型
  var type: Int32 = 0

  /// 持续时间
  var duration: Float = 0

  /// 位置：0=top, 1=bottom, 2=left, 3=right
  var position: Int32 = 0

  /// 字体大小
  var fontSize: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.4 触摸事件：坐标 + 动作类型
struct IAnts_SB_TouchEvent: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var x: Int32 = 0

  var y: Int32 = 0

  /// "tap", "swipe" 等
  var action: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.5 请求屏幕信息（无附加字段，通知端只要收到就发信息）
struct IAnts_SB_ScreenInfo: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.6 前台应用（无附加字段）
struct IAnts_SB_ForegroundApp: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.7 发送系统通知
struct IAnts_SB_Notification: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var title: String = String()

  var body: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.8 请求截图（无附加字段）
struct IAnts_SB_Screenshot: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.9 锁屏（无附加字段）
struct IAnts_SB_LockScreen: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.10 解锁（无附加字段）
struct IAnts_SB_UnlockScreen: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.11 高级弹窗消息：包含完整的弹窗参数
struct IAnts_SB_AdvancedAlert: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 可选：弹窗标题
  var title: String = String()

  /// 可选：弹窗内容
  var content: String = String()

  /// 可选：主按钮标题
  var defaultButtonTitle: String = String()

  /// 可选：备用按钮标题
  var alternateButtonTitle: String = String()

  /// 可选：第三按钮标题
  var otherButtonTitle: String = String()

  /// 可选：复选框标题数组
  var checkBoxTitles: [String] = []

  /// 可选：输入框标题数组
  var textFieldTitles: [String] = []

  /// 可选：输入框初始值数组
  var textFieldValues: [String] = []

  /// 可选：下拉菜单选项数组
  var popUpTitles: [String] = []

  /// 可选：下拉菜单默认选中索引
  var popUpSelection: Int32 = 0

  /// 可选：进度条数值（0.0~1.0）
  var progressValue: Float = 0

  /// 可选：自定义图标URL
  var iconURL: String = String()

  /// 可选：超时时间（秒）
  var dismissTime: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.12 错误消息：包含一个错误文本
struct IAnts_ErrorMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var text: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.13 UT 状态消息
struct IAnts_UT_StatusMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// utun 是否已创建
  var utunCreated: Bool = false

  /// 接口名称 (如 utun5)
  var interfaceName: String = String()

  /// 单元号
  var unit: Int32 = 0

  /// IP 地址
  var ipAddress: String = String()

  /// 目标地址
  var destAddress: String = String()

  /// MTU 大小
  var mtu: Int32 = 0

  /// 接口是否启用
  var interfaceUp: Bool = false

  /// 代理是否运行中
  var proxyRunning: Bool = false

  /// 代理 URL
  var proxyURL: String = String()

  /// 路由列表
  var routes: [String] = []

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.14 UT 获取配置
struct IAnts_UT_GetConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.15 UT 路由配置
struct IAnts_UT_RouterConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 转发策略: "direct", "proxy", "auto"
  var strategy: String = String()

  /// 白名单 IP/域名
  var whitelist: [String] = []

  /// 黑名单 IP/域名
  var blacklist: [String] = []

  /// 是否启用 DNS 劫持
  var enableDnsHijack: Bool = false

  /// DNS 服务器地址
  var dnsServer: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.16 UT 路由动作
struct IAnts_UT_RouterActionMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var action: IAnts_UT_RouterActionMessage.Action = .add

  /// 目标地址 (如 "default", "***********/24")
  var destination: String = String()

  /// 网关地址
  var gateway: String = String()

  /// 网络掩码
  var netmask: String = String()

  /// 接口名称
  var interface: String = String()

  /// 路由度量值
  var metric: Int32 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum Action: SwiftProtobuf.Enum, Swift.CaseIterable {
    typealias RawValue = Int

    /// 添加路由
    case add // = 0

    /// 删除路由
    case delete // = 1

    /// 修改路由
    case modify // = 2

    /// 恢复默认路由
    case restoreDefault // = 3
    case UNRECOGNIZED(Int)

    init() {
      self = .add
    }

    init?(rawValue: Int) {
      switch rawValue {
      case 0: self = .add
      case 1: self = .delete
      case 2: self = .modify
      case 3: self = .restoreDefault
      default: self = .UNRECOGNIZED(rawValue)
      }
    }

    var rawValue: Int {
      switch self {
      case .add: return 0
      case .delete: return 1
      case .modify: return 2
      case .restoreDefault: return 3
      case .UNRECOGNIZED(let i): return i
      }
    }

    // The compiler won't synthesize support with the UNRECOGNIZED case.
    static let allCases: [IAnts_UT_RouterActionMessage.Action] = [
      .add,
      .delete,
      .modify,
      .restoreDefault,
    ]

  }

  init() {}
}

/// 2.17 UT IP 配置
struct IAnts_UT_IPConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// IP 地址
  var ipAddress: String = String()

  /// 目标地址 (点对点)
  var destAddress: String = String()

  /// 网络掩码
  var netmask: String = String()

  /// MTU 大小
  var mtu: Int32 = 0

  /// 是否启用接口
  var bringUp: Bool = false

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.18 UT 代理配置
struct IAnts_UT_ProxyConfigMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var type: IAnts_UT_ProxyConfigMessage.ProxyType = .http

  /// 代理主机
  var host: String = String()

  /// 代理端口
  var port: Int32 = 0

  /// 用户名 (可选)
  var username: String = String()

  /// 密码 (可选)
  var password: String = String()

  /// 额外配置 (JSON 格式)
  var extraConfig: String = String()

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum ProxyType: SwiftProtobuf.Enum, Swift.CaseIterable {
    typealias RawValue = Int
    case http // = 0
    case https // = 1
    case socks5 // = 2
    case shadowsocks // = 3
    case UNRECOGNIZED(Int)

    init() {
      self = .http
    }

    init?(rawValue: Int) {
      switch rawValue {
      case 0: self = .http
      case 1: self = .https
      case 2: self = .socks5
      case 3: self = .shadowsocks
      default: self = .UNRECOGNIZED(rawValue)
      }
    }

    var rawValue: Int {
      switch self {
      case .http: return 0
      case .https: return 1
      case .socks5: return 2
      case .shadowsocks: return 3
      case .UNRECOGNIZED(let i): return i
      }
    }

    // The compiler won't synthesize support with the UNRECOGNIZED case.
    static let allCases: [IAnts_UT_ProxyConfigMessage.ProxyType] = [
      .http,
      .https,
      .socks5,
      .shadowsocks,
    ]

  }

  init() {}
}

/// 2.19 UT 代理动作
struct IAnts_UT_ProxyActionMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var action: IAnts_UT_ProxyActionMessage.Action = .start

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum Action: SwiftProtobuf.Enum, Swift.CaseIterable {
    typealias RawValue = Int

    /// 启动代理
    case start // = 0

    /// 停止代理
    case stop // = 1

    /// 重启代理
    case restart // = 2
    case UNRECOGNIZED(Int)

    init() {
      self = .start
    }

    init?(rawValue: Int) {
      switch rawValue {
      case 0: self = .start
      case 1: self = .stop
      case 2: self = .restart
      default: self = .UNRECOGNIZED(rawValue)
      }
    }

    var rawValue: Int {
      switch self {
      case .start: return 0
      case .stop: return 1
      case .restart: return 2
      case .UNRECOGNIZED(let i): return i
      }
    }

    // The compiler won't synthesize support with the UNRECOGNIZED case.
    static let allCases: [IAnts_UT_ProxyActionMessage.Action] = [
      .start,
      .stop,
      .restart,
    ]

  }

  init() {}
}

/// 2.20 UT 代理状态
struct IAnts_UT_ProxyStatusMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// 是否运行中
  var running: Bool = false

  /// 当前代理 URL
  var proxyURL: String = String()

  /// 发送字节数
  var bytesSent: Int64 = 0

  /// 接收字节数
  var bytesReceived: Int64 = 0

  /// 启动时间戳
  var startTime: Int64 = 0

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// 2.21 UT 恢复默认配置
struct IAnts_UT_DefaultMessage: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var unknownFields = SwiftProtobuf.UnknownStorage()

  init() {}
}

/// -----------------------------------------------------------------------------
/// 3. 最外层包装消息：IAntsMessage，使用 oneof 来保证“type”与“payload”对齐
///    “type”字段决定了具体 payload 使用哪一个子消息。
/// -----------------------------------------------------------------------------
struct IAnts_Message: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  var type: IAnts_MessageType = .heartbeat

  var body: IAnts_Message.OneOf_Body? = nil

  var heartbeat: IAnts_Heartbeat {
    get {
      if case .heartbeat(let v)? = body {return v}
      return IAnts_Heartbeat()
    }
    set {body = .heartbeat(newValue)}
  }

  var showAlert: IAnts_SB_Alert {
    get {
      if case .showAlert(let v)? = body {return v}
      return IAnts_SB_Alert()
    }
    set {body = .showAlert(newValue)}
  }

  var showToast: IAnts_SB_Toast {
    get {
      if case .showToast(let v)? = body {return v}
      return IAnts_SB_Toast()
    }
    set {body = .showToast(newValue)}
  }

  var touchEvent: IAnts_SB_TouchEvent {
    get {
      if case .touchEvent(let v)? = body {return v}
      return IAnts_SB_TouchEvent()
    }
    set {body = .touchEvent(newValue)}
  }

  var screenInfo: IAnts_SB_ScreenInfo {
    get {
      if case .screenInfo(let v)? = body {return v}
      return IAnts_SB_ScreenInfo()
    }
    set {body = .screenInfo(newValue)}
  }

  var foregroundApp: IAnts_SB_ForegroundApp {
    get {
      if case .foregroundApp(let v)? = body {return v}
      return IAnts_SB_ForegroundApp()
    }
    set {body = .foregroundApp(newValue)}
  }

  var notification: IAnts_SB_Notification {
    get {
      if case .notification(let v)? = body {return v}
      return IAnts_SB_Notification()
    }
    set {body = .notification(newValue)}
  }

  var screenshot: IAnts_SB_Screenshot {
    get {
      if case .screenshot(let v)? = body {return v}
      return IAnts_SB_Screenshot()
    }
    set {body = .screenshot(newValue)}
  }

  var lockScreen: IAnts_SB_LockScreen {
    get {
      if case .lockScreen(let v)? = body {return v}
      return IAnts_SB_LockScreen()
    }
    set {body = .lockScreen(newValue)}
  }

  var unlockScreen: IAnts_SB_UnlockScreen {
    get {
      if case .unlockScreen(let v)? = body {return v}
      return IAnts_SB_UnlockScreen()
    }
    set {body = .unlockScreen(newValue)}
  }

  var advancedAlert: IAnts_SB_AdvancedAlert {
    get {
      if case .advancedAlert(let v)? = body {return v}
      return IAnts_SB_AdvancedAlert()
    }
    set {body = .advancedAlert(newValue)}
  }

  var error: IAnts_ErrorMessage {
    get {
      if case .error(let v)? = body {return v}
      return IAnts_ErrorMessage()
    }
    set {body = .error(newValue)}
  }

  /// UT 消息 (300-399)
  var utStatus: IAnts_UT_StatusMessage {
    get {
      if case .utStatus(let v)? = body {return v}
      return IAnts_UT_StatusMessage()
    }
    set {body = .utStatus(newValue)}
  }

  var utGetConfig: IAnts_UT_GetConfigMessage {
    get {
      if case .utGetConfig(let v)? = body {return v}
      return IAnts_UT_GetConfigMessage()
    }
    set {body = .utGetConfig(newValue)}
  }

  var utRouterConfig: IAnts_UT_RouterConfigMessage {
    get {
      if case .utRouterConfig(let v)? = body {return v}
      return IAnts_UT_RouterConfigMessage()
    }
    set {body = .utRouterConfig(newValue)}
  }

  var utRouterAction: IAnts_UT_RouterActionMessage {
    get {
      if case .utRouterAction(let v)? = body {return v}
      return IAnts_UT_RouterActionMessage()
    }
    set {body = .utRouterAction(newValue)}
  }

  var utIpConfig: IAnts_UT_IPConfigMessage {
    get {
      if case .utIpConfig(let v)? = body {return v}
      return IAnts_UT_IPConfigMessage()
    }
    set {body = .utIpConfig(newValue)}
  }

  var utProxyConfig: IAnts_UT_ProxyConfigMessage {
    get {
      if case .utProxyConfig(let v)? = body {return v}
      return IAnts_UT_ProxyConfigMessage()
    }
    set {body = .utProxyConfig(newValue)}
  }

  var utProxyAction: IAnts_UT_ProxyActionMessage {
    get {
      if case .utProxyAction(let v)? = body {return v}
      return IAnts_UT_ProxyActionMessage()
    }
    set {body = .utProxyAction(newValue)}
  }

  var utProxyStatus: IAnts_UT_ProxyStatusMessage {
    get {
      if case .utProxyStatus(let v)? = body {return v}
      return IAnts_UT_ProxyStatusMessage()
    }
    set {body = .utProxyStatus(newValue)}
  }

  var utDefault: IAnts_UT_DefaultMessage {
    get {
      if case .utDefault(let v)? = body {return v}
      return IAnts_UT_DefaultMessage()
    }
    set {body = .utDefault(newValue)}
  }

  var unknownFields = SwiftProtobuf.UnknownStorage()

  enum OneOf_Body: Equatable, Sendable {
    case heartbeat(IAnts_Heartbeat)
    case showAlert(IAnts_SB_Alert)
    case showToast(IAnts_SB_Toast)
    case touchEvent(IAnts_SB_TouchEvent)
    case screenInfo(IAnts_SB_ScreenInfo)
    case foregroundApp(IAnts_SB_ForegroundApp)
    case notification(IAnts_SB_Notification)
    case screenshot(IAnts_SB_Screenshot)
    case lockScreen(IAnts_SB_LockScreen)
    case unlockScreen(IAnts_SB_UnlockScreen)
    case advancedAlert(IAnts_SB_AdvancedAlert)
    case error(IAnts_ErrorMessage)
    /// UT 消息 (300-399)
    case utStatus(IAnts_UT_StatusMessage)
    case utGetConfig(IAnts_UT_GetConfigMessage)
    case utRouterConfig(IAnts_UT_RouterConfigMessage)
    case utRouterAction(IAnts_UT_RouterActionMessage)
    case utIpConfig(IAnts_UT_IPConfigMessage)
    case utProxyConfig(IAnts_UT_ProxyConfigMessage)
    case utProxyAction(IAnts_UT_ProxyActionMessage)
    case utProxyStatus(IAnts_UT_ProxyStatusMessage)
    case utDefault(IAnts_UT_DefaultMessage)

  }

  init() {}
}

// MARK: - Code below here is support for the SwiftProtobuf runtime.

fileprivate let _protobuf_package = "iAnts"

extension IAnts_MessageType: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "HEARTBEAT"),
    10: .same(proto: "IANTS_VERSION"),
    11: .same(proto: "IANTS_HTTP_START"),
    12: .same(proto: "IANTS_HTTP_SHUTDOWN"),
    13: .same(proto: "IANTS_HTTP_RESTART"),
    100: .same(proto: "SB_SHOW_ALERT"),
    101: .same(proto: "SB_SHOW_TOAST"),
    102: .same(proto: "SB_TOUCH_EVENT"),
    103: .same(proto: "SB_SCREEN_INFO"),
    104: .same(proto: "SB_FOREGROUND_APP"),
    105: .same(proto: "SB_NOTIFICATION"),
    106: .same(proto: "SB_SCREENSHOT"),
    107: .same(proto: "SB_LOCK_SCREEN"),
    108: .same(proto: "SB_UNLOCK_SCREEN"),
    109: .same(proto: "SB_ADVANCED_ALERT_BOX"),
    200: .same(proto: "RTC_STATUS"),
    201: .same(proto: "RTC_DATAANT_ACTION"),
    202: .same(proto: "RTC_IANTS_ACTION"),
    203: .same(proto: "RTC_SCREENSHOT"),
    204: .same(proto: "RTC_SCREEN_CAPTURE"),
    205: .same(proto: "RTC_RECORD_AUDIO"),
    300: .same(proto: "UT_STATUS"),
    301: .same(proto: "UT_GET_CONFIG"),
    302: .same(proto: "UT_ROUTER_CONFIG"),
    303: .same(proto: "UT_ROUTER_ACTION"),
    304: .same(proto: "UT_IP_CONFIG"),
    305: .same(proto: "UT_PROXY_CONFIG"),
    306: .same(proto: "UT_PROXY_ACTION"),
    307: .same(proto: "UT_PROXY_STATUS"),
    399: .same(proto: "UT_DEAFULT"),
    999: .same(proto: "ERROR"),
  ]
}

extension IAnts_Heartbeat: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Heartbeat"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "version"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.version) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.version.isEmpty {
      try visitor.visitSingularStringField(value: self.version, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_Heartbeat, rhs: IAnts_Heartbeat) -> Bool {
    if lhs.version != rhs.version {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Alert: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Alert"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "title"),
    2: .same(proto: "content"),
    3: .same(proto: "dismissTime"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.title) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.dismissTime) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.title.isEmpty {
      try visitor.visitSingularStringField(value: self.title, fieldNumber: 1)
    }
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 2)
    }
    if self.dismissTime != 0 {
      try visitor.visitSingularInt32Field(value: self.dismissTime, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Alert, rhs: IAnts_SB_Alert) -> Bool {
    if lhs.title != rhs.title {return false}
    if lhs.content != rhs.content {return false}
    if lhs.dismissTime != rhs.dismissTime {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Toast: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Toast"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "content"),
    2: .same(proto: "type"),
    3: .same(proto: "duration"),
    4: .same(proto: "position"),
    5: .same(proto: "fontSize"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.type) }()
      case 3: try { try decoder.decodeSingularFloatField(value: &self.duration) }()
      case 4: try { try decoder.decodeSingularInt32Field(value: &self.position) }()
      case 5: try { try decoder.decodeSingularInt32Field(value: &self.fontSize) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 1)
    }
    if self.type != 0 {
      try visitor.visitSingularInt32Field(value: self.type, fieldNumber: 2)
    }
    if self.duration.bitPattern != 0 {
      try visitor.visitSingularFloatField(value: self.duration, fieldNumber: 3)
    }
    if self.position != 0 {
      try visitor.visitSingularInt32Field(value: self.position, fieldNumber: 4)
    }
    if self.fontSize != 0 {
      try visitor.visitSingularInt32Field(value: self.fontSize, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Toast, rhs: IAnts_SB_Toast) -> Bool {
    if lhs.content != rhs.content {return false}
    if lhs.type != rhs.type {return false}
    if lhs.duration != rhs.duration {return false}
    if lhs.position != rhs.position {return false}
    if lhs.fontSize != rhs.fontSize {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_TouchEvent: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_TouchEvent"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "x"),
    2: .same(proto: "y"),
    3: .same(proto: "action"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self.x) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self.y) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.action) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.x != 0 {
      try visitor.visitSingularInt32Field(value: self.x, fieldNumber: 1)
    }
    if self.y != 0 {
      try visitor.visitSingularInt32Field(value: self.y, fieldNumber: 2)
    }
    if !self.action.isEmpty {
      try visitor.visitSingularStringField(value: self.action, fieldNumber: 3)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_TouchEvent, rhs: IAnts_SB_TouchEvent) -> Bool {
    if lhs.x != rhs.x {return false}
    if lhs.y != rhs.y {return false}
    if lhs.action != rhs.action {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_ScreenInfo: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_ScreenInfo"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_ScreenInfo, rhs: IAnts_SB_ScreenInfo) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_ForegroundApp: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_ForegroundApp"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_ForegroundApp, rhs: IAnts_SB_ForegroundApp) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Notification: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Notification"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "title"),
    2: .same(proto: "body"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.title) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.body) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.title.isEmpty {
      try visitor.visitSingularStringField(value: self.title, fieldNumber: 1)
    }
    if !self.body.isEmpty {
      try visitor.visitSingularStringField(value: self.body, fieldNumber: 2)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Notification, rhs: IAnts_SB_Notification) -> Bool {
    if lhs.title != rhs.title {return false}
    if lhs.body != rhs.body {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_Screenshot: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_Screenshot"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_Screenshot, rhs: IAnts_SB_Screenshot) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_LockScreen: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_LockScreen"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_LockScreen, rhs: IAnts_SB_LockScreen) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_UnlockScreen: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_UnlockScreen"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_UnlockScreen, rhs: IAnts_SB_UnlockScreen) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_SB_AdvancedAlert: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".SB_AdvancedAlert"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "title"),
    2: .same(proto: "content"),
    3: .same(proto: "defaultButtonTitle"),
    4: .same(proto: "alternateButtonTitle"),
    5: .same(proto: "otherButtonTitle"),
    6: .same(proto: "checkBoxTitles"),
    7: .same(proto: "textFieldTitles"),
    8: .same(proto: "textFieldValues"),
    9: .same(proto: "popUpTitles"),
    10: .same(proto: "popUpSelection"),
    11: .same(proto: "progressValue"),
    12: .same(proto: "iconURL"),
    13: .same(proto: "dismissTime"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.title) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.content) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.defaultButtonTitle) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.alternateButtonTitle) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.otherButtonTitle) }()
      case 6: try { try decoder.decodeRepeatedStringField(value: &self.checkBoxTitles) }()
      case 7: try { try decoder.decodeRepeatedStringField(value: &self.textFieldTitles) }()
      case 8: try { try decoder.decodeRepeatedStringField(value: &self.textFieldValues) }()
      case 9: try { try decoder.decodeRepeatedStringField(value: &self.popUpTitles) }()
      case 10: try { try decoder.decodeSingularInt32Field(value: &self.popUpSelection) }()
      case 11: try { try decoder.decodeSingularFloatField(value: &self.progressValue) }()
      case 12: try { try decoder.decodeSingularStringField(value: &self.iconURL) }()
      case 13: try { try decoder.decodeSingularInt32Field(value: &self.dismissTime) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.title.isEmpty {
      try visitor.visitSingularStringField(value: self.title, fieldNumber: 1)
    }
    if !self.content.isEmpty {
      try visitor.visitSingularStringField(value: self.content, fieldNumber: 2)
    }
    if !self.defaultButtonTitle.isEmpty {
      try visitor.visitSingularStringField(value: self.defaultButtonTitle, fieldNumber: 3)
    }
    if !self.alternateButtonTitle.isEmpty {
      try visitor.visitSingularStringField(value: self.alternateButtonTitle, fieldNumber: 4)
    }
    if !self.otherButtonTitle.isEmpty {
      try visitor.visitSingularStringField(value: self.otherButtonTitle, fieldNumber: 5)
    }
    if !self.checkBoxTitles.isEmpty {
      try visitor.visitRepeatedStringField(value: self.checkBoxTitles, fieldNumber: 6)
    }
    if !self.textFieldTitles.isEmpty {
      try visitor.visitRepeatedStringField(value: self.textFieldTitles, fieldNumber: 7)
    }
    if !self.textFieldValues.isEmpty {
      try visitor.visitRepeatedStringField(value: self.textFieldValues, fieldNumber: 8)
    }
    if !self.popUpTitles.isEmpty {
      try visitor.visitRepeatedStringField(value: self.popUpTitles, fieldNumber: 9)
    }
    if self.popUpSelection != 0 {
      try visitor.visitSingularInt32Field(value: self.popUpSelection, fieldNumber: 10)
    }
    if self.progressValue.bitPattern != 0 {
      try visitor.visitSingularFloatField(value: self.progressValue, fieldNumber: 11)
    }
    if !self.iconURL.isEmpty {
      try visitor.visitSingularStringField(value: self.iconURL, fieldNumber: 12)
    }
    if self.dismissTime != 0 {
      try visitor.visitSingularInt32Field(value: self.dismissTime, fieldNumber: 13)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_SB_AdvancedAlert, rhs: IAnts_SB_AdvancedAlert) -> Bool {
    if lhs.title != rhs.title {return false}
    if lhs.content != rhs.content {return false}
    if lhs.defaultButtonTitle != rhs.defaultButtonTitle {return false}
    if lhs.alternateButtonTitle != rhs.alternateButtonTitle {return false}
    if lhs.otherButtonTitle != rhs.otherButtonTitle {return false}
    if lhs.checkBoxTitles != rhs.checkBoxTitles {return false}
    if lhs.textFieldTitles != rhs.textFieldTitles {return false}
    if lhs.textFieldValues != rhs.textFieldValues {return false}
    if lhs.popUpTitles != rhs.popUpTitles {return false}
    if lhs.popUpSelection != rhs.popUpSelection {return false}
    if lhs.progressValue != rhs.progressValue {return false}
    if lhs.iconURL != rhs.iconURL {return false}
    if lhs.dismissTime != rhs.dismissTime {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_ErrorMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".ErrorMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "text"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.text) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.text.isEmpty {
      try visitor.visitSingularStringField(value: self.text, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_ErrorMessage, rhs: IAnts_ErrorMessage) -> Bool {
    if lhs.text != rhs.text {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_StatusMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_StatusMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "utun_created"),
    2: .standard(proto: "interface_name"),
    3: .same(proto: "unit"),
    4: .standard(proto: "ip_address"),
    5: .standard(proto: "dest_address"),
    6: .same(proto: "mtu"),
    7: .standard(proto: "interface_up"),
    8: .standard(proto: "proxy_running"),
    9: .standard(proto: "proxy_url"),
    10: .same(proto: "routes"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.utunCreated) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.interfaceName) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.unit) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.ipAddress) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.destAddress) }()
      case 6: try { try decoder.decodeSingularInt32Field(value: &self.mtu) }()
      case 7: try { try decoder.decodeSingularBoolField(value: &self.interfaceUp) }()
      case 8: try { try decoder.decodeSingularBoolField(value: &self.proxyRunning) }()
      case 9: try { try decoder.decodeSingularStringField(value: &self.proxyURL) }()
      case 10: try { try decoder.decodeRepeatedStringField(value: &self.routes) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.utunCreated != false {
      try visitor.visitSingularBoolField(value: self.utunCreated, fieldNumber: 1)
    }
    if !self.interfaceName.isEmpty {
      try visitor.visitSingularStringField(value: self.interfaceName, fieldNumber: 2)
    }
    if self.unit != 0 {
      try visitor.visitSingularInt32Field(value: self.unit, fieldNumber: 3)
    }
    if !self.ipAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.ipAddress, fieldNumber: 4)
    }
    if !self.destAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.destAddress, fieldNumber: 5)
    }
    if self.mtu != 0 {
      try visitor.visitSingularInt32Field(value: self.mtu, fieldNumber: 6)
    }
    if self.interfaceUp != false {
      try visitor.visitSingularBoolField(value: self.interfaceUp, fieldNumber: 7)
    }
    if self.proxyRunning != false {
      try visitor.visitSingularBoolField(value: self.proxyRunning, fieldNumber: 8)
    }
    if !self.proxyURL.isEmpty {
      try visitor.visitSingularStringField(value: self.proxyURL, fieldNumber: 9)
    }
    if !self.routes.isEmpty {
      try visitor.visitRepeatedStringField(value: self.routes, fieldNumber: 10)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_StatusMessage, rhs: IAnts_UT_StatusMessage) -> Bool {
    if lhs.utunCreated != rhs.utunCreated {return false}
    if lhs.interfaceName != rhs.interfaceName {return false}
    if lhs.unit != rhs.unit {return false}
    if lhs.ipAddress != rhs.ipAddress {return false}
    if lhs.destAddress != rhs.destAddress {return false}
    if lhs.mtu != rhs.mtu {return false}
    if lhs.interfaceUp != rhs.interfaceUp {return false}
    if lhs.proxyRunning != rhs.proxyRunning {return false}
    if lhs.proxyURL != rhs.proxyURL {return false}
    if lhs.routes != rhs.routes {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_GetConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_GetConfigMessage"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_GetConfigMessage, rhs: IAnts_UT_GetConfigMessage) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_RouterConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_RouterConfigMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "strategy"),
    2: .same(proto: "whitelist"),
    3: .same(proto: "blacklist"),
    4: .standard(proto: "enable_dns_hijack"),
    5: .standard(proto: "dns_server"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.strategy) }()
      case 2: try { try decoder.decodeRepeatedStringField(value: &self.whitelist) }()
      case 3: try { try decoder.decodeRepeatedStringField(value: &self.blacklist) }()
      case 4: try { try decoder.decodeSingularBoolField(value: &self.enableDnsHijack) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.dnsServer) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.strategy.isEmpty {
      try visitor.visitSingularStringField(value: self.strategy, fieldNumber: 1)
    }
    if !self.whitelist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.whitelist, fieldNumber: 2)
    }
    if !self.blacklist.isEmpty {
      try visitor.visitRepeatedStringField(value: self.blacklist, fieldNumber: 3)
    }
    if self.enableDnsHijack != false {
      try visitor.visitSingularBoolField(value: self.enableDnsHijack, fieldNumber: 4)
    }
    if !self.dnsServer.isEmpty {
      try visitor.visitSingularStringField(value: self.dnsServer, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_RouterConfigMessage, rhs: IAnts_UT_RouterConfigMessage) -> Bool {
    if lhs.strategy != rhs.strategy {return false}
    if lhs.whitelist != rhs.whitelist {return false}
    if lhs.blacklist != rhs.blacklist {return false}
    if lhs.enableDnsHijack != rhs.enableDnsHijack {return false}
    if lhs.dnsServer != rhs.dnsServer {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_RouterActionMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_RouterActionMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "action"),
    2: .same(proto: "destination"),
    3: .same(proto: "gateway"),
    4: .same(proto: "netmask"),
    5: .same(proto: "interface"),
    6: .same(proto: "metric"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.action) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.destination) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.gateway) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.netmask) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.interface) }()
      case 6: try { try decoder.decodeSingularInt32Field(value: &self.metric) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.action != .add {
      try visitor.visitSingularEnumField(value: self.action, fieldNumber: 1)
    }
    if !self.destination.isEmpty {
      try visitor.visitSingularStringField(value: self.destination, fieldNumber: 2)
    }
    if !self.gateway.isEmpty {
      try visitor.visitSingularStringField(value: self.gateway, fieldNumber: 3)
    }
    if !self.netmask.isEmpty {
      try visitor.visitSingularStringField(value: self.netmask, fieldNumber: 4)
    }
    if !self.interface.isEmpty {
      try visitor.visitSingularStringField(value: self.interface, fieldNumber: 5)
    }
    if self.metric != 0 {
      try visitor.visitSingularInt32Field(value: self.metric, fieldNumber: 6)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_RouterActionMessage, rhs: IAnts_UT_RouterActionMessage) -> Bool {
    if lhs.action != rhs.action {return false}
    if lhs.destination != rhs.destination {return false}
    if lhs.gateway != rhs.gateway {return false}
    if lhs.netmask != rhs.netmask {return false}
    if lhs.interface != rhs.interface {return false}
    if lhs.metric != rhs.metric {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_RouterActionMessage.Action: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "ADD"),
    1: .same(proto: "DELETE"),
    2: .same(proto: "MODIFY"),
    3: .same(proto: "RESTORE_DEFAULT"),
  ]
}

extension IAnts_UT_IPConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_IPConfigMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .standard(proto: "ip_address"),
    2: .standard(proto: "dest_address"),
    3: .same(proto: "netmask"),
    4: .same(proto: "mtu"),
    5: .standard(proto: "bring_up"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.ipAddress) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.destAddress) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self.netmask) }()
      case 4: try { try decoder.decodeSingularInt32Field(value: &self.mtu) }()
      case 5: try { try decoder.decodeSingularBoolField(value: &self.bringUp) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if !self.ipAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.ipAddress, fieldNumber: 1)
    }
    if !self.destAddress.isEmpty {
      try visitor.visitSingularStringField(value: self.destAddress, fieldNumber: 2)
    }
    if !self.netmask.isEmpty {
      try visitor.visitSingularStringField(value: self.netmask, fieldNumber: 3)
    }
    if self.mtu != 0 {
      try visitor.visitSingularInt32Field(value: self.mtu, fieldNumber: 4)
    }
    if self.bringUp != false {
      try visitor.visitSingularBoolField(value: self.bringUp, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_IPConfigMessage, rhs: IAnts_UT_IPConfigMessage) -> Bool {
    if lhs.ipAddress != rhs.ipAddress {return false}
    if lhs.destAddress != rhs.destAddress {return false}
    if lhs.netmask != rhs.netmask {return false}
    if lhs.mtu != rhs.mtu {return false}
    if lhs.bringUp != rhs.bringUp {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_ProxyConfigMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_ProxyConfigMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "type"),
    2: .same(proto: "host"),
    3: .same(proto: "port"),
    4: .same(proto: "username"),
    5: .same(proto: "password"),
    6: .standard(proto: "extra_config"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.type) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.host) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self.port) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self.username) }()
      case 5: try { try decoder.decodeSingularStringField(value: &self.password) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self.extraConfig) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.type != .http {
      try visitor.visitSingularEnumField(value: self.type, fieldNumber: 1)
    }
    if !self.host.isEmpty {
      try visitor.visitSingularStringField(value: self.host, fieldNumber: 2)
    }
    if self.port != 0 {
      try visitor.visitSingularInt32Field(value: self.port, fieldNumber: 3)
    }
    if !self.username.isEmpty {
      try visitor.visitSingularStringField(value: self.username, fieldNumber: 4)
    }
    if !self.password.isEmpty {
      try visitor.visitSingularStringField(value: self.password, fieldNumber: 5)
    }
    if !self.extraConfig.isEmpty {
      try visitor.visitSingularStringField(value: self.extraConfig, fieldNumber: 6)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_ProxyConfigMessage, rhs: IAnts_UT_ProxyConfigMessage) -> Bool {
    if lhs.type != rhs.type {return false}
    if lhs.host != rhs.host {return false}
    if lhs.port != rhs.port {return false}
    if lhs.username != rhs.username {return false}
    if lhs.password != rhs.password {return false}
    if lhs.extraConfig != rhs.extraConfig {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_ProxyConfigMessage.ProxyType: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "HTTP"),
    1: .same(proto: "HTTPS"),
    2: .same(proto: "SOCKS5"),
    3: .same(proto: "SHADOWSOCKS"),
  ]
}

extension IAnts_UT_ProxyActionMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_ProxyActionMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "action"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.action) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.action != .start {
      try visitor.visitSingularEnumField(value: self.action, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_ProxyActionMessage, rhs: IAnts_UT_ProxyActionMessage) -> Bool {
    if lhs.action != rhs.action {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_ProxyActionMessage.Action: SwiftProtobuf._ProtoNameProviding {
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    0: .same(proto: "START"),
    1: .same(proto: "STOP"),
    2: .same(proto: "RESTART"),
  ]
}

extension IAnts_UT_ProxyStatusMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_ProxyStatusMessage"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "running"),
    2: .standard(proto: "proxy_url"),
    3: .standard(proto: "bytes_sent"),
    4: .standard(proto: "bytes_received"),
    5: .standard(proto: "start_time"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.running) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self.proxyURL) }()
      case 3: try { try decoder.decodeSingularInt64Field(value: &self.bytesSent) }()
      case 4: try { try decoder.decodeSingularInt64Field(value: &self.bytesReceived) }()
      case 5: try { try decoder.decodeSingularInt64Field(value: &self.startTime) }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    if self.running != false {
      try visitor.visitSingularBoolField(value: self.running, fieldNumber: 1)
    }
    if !self.proxyURL.isEmpty {
      try visitor.visitSingularStringField(value: self.proxyURL, fieldNumber: 2)
    }
    if self.bytesSent != 0 {
      try visitor.visitSingularInt64Field(value: self.bytesSent, fieldNumber: 3)
    }
    if self.bytesReceived != 0 {
      try visitor.visitSingularInt64Field(value: self.bytesReceived, fieldNumber: 4)
    }
    if self.startTime != 0 {
      try visitor.visitSingularInt64Field(value: self.startTime, fieldNumber: 5)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_ProxyStatusMessage, rhs: IAnts_UT_ProxyStatusMessage) -> Bool {
    if lhs.running != rhs.running {return false}
    if lhs.proxyURL != rhs.proxyURL {return false}
    if lhs.bytesSent != rhs.bytesSent {return false}
    if lhs.bytesReceived != rhs.bytesReceived {return false}
    if lhs.startTime != rhs.startTime {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_UT_DefaultMessage: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".UT_DefaultMessage"
  static let _protobuf_nameMap = SwiftProtobuf._NameMap()

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_UT_DefaultMessage, rhs: IAnts_UT_DefaultMessage) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension IAnts_Message: SwiftProtobuf.Message, SwiftProtobuf._MessageImplementationBase, SwiftProtobuf._ProtoNameProviding {
  static let protoMessageName: String = _protobuf_package + ".Message"
  static let _protobuf_nameMap: SwiftProtobuf._NameMap = [
    1: .same(proto: "type"),
    2: .same(proto: "heartbeat"),
    3: .same(proto: "showAlert"),
    4: .same(proto: "showToast"),
    5: .same(proto: "touchEvent"),
    6: .same(proto: "screenInfo"),
    7: .same(proto: "foregroundApp"),
    8: .same(proto: "notification"),
    9: .same(proto: "screenshot"),
    10: .same(proto: "lockScreen"),
    11: .same(proto: "unlockScreen"),
    12: .same(proto: "advancedAlert"),
    13: .same(proto: "error"),
    14: .standard(proto: "ut_status"),
    15: .standard(proto: "ut_get_config"),
    16: .standard(proto: "ut_router_config"),
    17: .standard(proto: "ut_router_action"),
    18: .standard(proto: "ut_ip_config"),
    19: .standard(proto: "ut_proxy_config"),
    20: .standard(proto: "ut_proxy_action"),
    21: .standard(proto: "ut_proxy_status"),
    22: .standard(proto: "ut_default"),
  ]

  mutating func decodeMessage<D: SwiftProtobuf.Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self.type) }()
      case 2: try {
        var v: IAnts_Heartbeat?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .heartbeat(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .heartbeat(v)
        }
      }()
      case 3: try {
        var v: IAnts_SB_Alert?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .showAlert(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .showAlert(v)
        }
      }()
      case 4: try {
        var v: IAnts_SB_Toast?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .showToast(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .showToast(v)
        }
      }()
      case 5: try {
        var v: IAnts_SB_TouchEvent?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .touchEvent(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .touchEvent(v)
        }
      }()
      case 6: try {
        var v: IAnts_SB_ScreenInfo?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .screenInfo(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .screenInfo(v)
        }
      }()
      case 7: try {
        var v: IAnts_SB_ForegroundApp?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .foregroundApp(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .foregroundApp(v)
        }
      }()
      case 8: try {
        var v: IAnts_SB_Notification?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .notification(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .notification(v)
        }
      }()
      case 9: try {
        var v: IAnts_SB_Screenshot?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .screenshot(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .screenshot(v)
        }
      }()
      case 10: try {
        var v: IAnts_SB_LockScreen?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .lockScreen(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .lockScreen(v)
        }
      }()
      case 11: try {
        var v: IAnts_SB_UnlockScreen?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .unlockScreen(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .unlockScreen(v)
        }
      }()
      case 12: try {
        var v: IAnts_SB_AdvancedAlert?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .advancedAlert(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .advancedAlert(v)
        }
      }()
      case 13: try {
        var v: IAnts_ErrorMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .error(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .error(v)
        }
      }()
      case 14: try {
        var v: IAnts_UT_StatusMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utStatus(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utStatus(v)
        }
      }()
      case 15: try {
        var v: IAnts_UT_GetConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utGetConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utGetConfig(v)
        }
      }()
      case 16: try {
        var v: IAnts_UT_RouterConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utRouterConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utRouterConfig(v)
        }
      }()
      case 17: try {
        var v: IAnts_UT_RouterActionMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utRouterAction(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utRouterAction(v)
        }
      }()
      case 18: try {
        var v: IAnts_UT_IPConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utIpConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utIpConfig(v)
        }
      }()
      case 19: try {
        var v: IAnts_UT_ProxyConfigMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utProxyConfig(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utProxyConfig(v)
        }
      }()
      case 20: try {
        var v: IAnts_UT_ProxyActionMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utProxyAction(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utProxyAction(v)
        }
      }()
      case 21: try {
        var v: IAnts_UT_ProxyStatusMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utProxyStatus(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utProxyStatus(v)
        }
      }()
      case 22: try {
        var v: IAnts_UT_DefaultMessage?
        var hadOneofValue = false
        if let current = self.body {
          hadOneofValue = true
          if case .utDefault(let m) = current {v = m}
        }
        try decoder.decodeSingularMessageField(value: &v)
        if let v = v {
          if hadOneofValue {try decoder.handleConflictingOneOf()}
          self.body = .utDefault(v)
        }
      }()
      default: break
      }
    }
  }

  func traverse<V: SwiftProtobuf.Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if self.type != .heartbeat {
      try visitor.visitSingularEnumField(value: self.type, fieldNumber: 1)
    }
    switch self.body {
    case .heartbeat?: try {
      guard case .heartbeat(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    }()
    case .showAlert?: try {
      guard case .showAlert(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
    }()
    case .showToast?: try {
      guard case .showToast(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
    }()
    case .touchEvent?: try {
      guard case .touchEvent(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 5)
    }()
    case .screenInfo?: try {
      guard case .screenInfo(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 6)
    }()
    case .foregroundApp?: try {
      guard case .foregroundApp(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
    }()
    case .notification?: try {
      guard case .notification(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
    }()
    case .screenshot?: try {
      guard case .screenshot(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 9)
    }()
    case .lockScreen?: try {
      guard case .lockScreen(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 10)
    }()
    case .unlockScreen?: try {
      guard case .unlockScreen(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 11)
    }()
    case .advancedAlert?: try {
      guard case .advancedAlert(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 12)
    }()
    case .error?: try {
      guard case .error(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 13)
    }()
    case .utStatus?: try {
      guard case .utStatus(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 14)
    }()
    case .utGetConfig?: try {
      guard case .utGetConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 15)
    }()
    case .utRouterConfig?: try {
      guard case .utRouterConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 16)
    }()
    case .utRouterAction?: try {
      guard case .utRouterAction(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 17)
    }()
    case .utIpConfig?: try {
      guard case .utIpConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 18)
    }()
    case .utProxyConfig?: try {
      guard case .utProxyConfig(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 19)
    }()
    case .utProxyAction?: try {
      guard case .utProxyAction(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 20)
    }()
    case .utProxyStatus?: try {
      guard case .utProxyStatus(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 21)
    }()
    case .utDefault?: try {
      guard case .utDefault(let v)? = self.body else { preconditionFailure() }
      try visitor.visitSingularMessageField(value: v, fieldNumber: 22)
    }()
    case nil: break
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  static func ==(lhs: IAnts_Message, rhs: IAnts_Message) -> Bool {
    if lhs.type != rhs.type {return false}
    if lhs.body != rhs.body {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}
