//
//  InputEventHandler.h
//  iAntsRTC
//
//  Created by YC on 2025/01/21.
//

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>

NS_ASSUME_NONNULL_BEGIN

@interface InputEventHandler : NSObject

/**
 设置当前屏幕尺寸（用于归一化指针坐标）

 @param width 屏幕宽度
 @param height 屏幕高度
 */
+ (void)setScreenWidth:(size_t)width height:(size_t)height;

/**
 处理键盘输入事件

 @param text 输入的文本
 */
+ (void)handleKeyboardInputWithText:(NSString *)text;

/**
 处理触摸按下事件

 @param x 归一化横坐标 (0.0-1.0)
 @param y 归一化纵坐标 (0.0-1.0)
 */
+ (void)handleTouchDownWithX:(CGFloat)x y:(CGFloat)y;

/**
 处理触摸移动事件

 @param x 归一化横坐标 (0.0-1.0)
 @param y 归一化纵坐标 (0.0-1.0)
 */
+ (void)handleTouchMoveWithX:(CGFloat)x y:(CGFloat)y;

/**
 处理触摸抬起事件

 @param x 归一化横坐标 (0.0-1.0)
 @param y 归一化纵坐标 (0.0-1.0)
 */
+ (void)handleTouchUpWithX:(CGFloat)x y:(CGFloat)y;

/**
 处理滚动事件

 @param deltaH 滚动增量
 */
+ (void)handleScrollWithDeltaH:(CGFloat)deltaH;

/**
 处理 Shell 命令事件（适配 Android 操作）

 @param cmd Shell 命令字符串
 */
+ (void)handleShellCommand:(NSString *)cmd;

@end

NS_ASSUME_NONNULL_END
